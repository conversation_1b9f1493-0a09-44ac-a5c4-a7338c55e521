<?php

namespace App\Livewire\Administration\Hiring;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\Hiring\HiringStage;
use App\Models\Hiring\HiringCandidate;
use App\Services\Administration\Hiring\HiringService;

class HiringCandidateFilter extends Component
{
    use WithPagination;

    public $search = '';
    public $status = '';
    public $stage = '';
    public $dateFrom = '';
    public $dateTo = '';
    public $perPage = 15;

    protected $queryString = [
        'search' => ['except' => ''],
        'status' => ['except' => ''],
        'stage' => ['except' => ''],
        'dateFrom' => ['except' => ''],
        'dateTo' => ['except' => ''],
        'page' => ['except' => 1],
    ];

    protected $hiringService;

    public function boot(HiringService $hiringService)
    {
        $this->hiringService = $hiringService;
    }

    public function updatingSearch()
    {
        $this->resetPage();
    }

    public function updatingStatus()
    {
        $this->resetPage();
    }

    public function updatingStage()
    {
        $this->resetPage();
    }

    public function updatingDateFrom()
    {
        $this->resetPage();
    }

    public function updatingDateTo()
    {
        $this->resetPage();
    }

    public function clearFilters()
    {
        $this->reset(['search', 'status', 'stage', 'dateFrom', 'dateTo']);
        $this->resetPage();
    }

    public function render()
    {
        $request = (object) [
            'search' => $this->search,
            'status' => $this->status,
            'stage' => $this->stage,
            'date_from' => $this->dateFrom,
            'date_to' => $this->dateTo,
        ];

        $candidates = $this->hiringService->getCandidatesQuery($request)->paginate($this->perPage);
        $stages = HiringStage::active()->ordered()->get();
        $statuses = HiringCandidate::getStatuses();

        return view('livewire.administration.hiring.hiring-candidate-filter', [
            'candidates' => $candidates,
            'stages' => $stages,
            'statuses' => $statuses,
        ]);
    }
}
