<?php

namespace App\Livewire\Administration\Hiring;

use Livewire\Component;
use Livewire\WithFileUploads;
use App\Models\Hiring\HiringStage;
use App\Models\Hiring\HiringCandidate;
use App\Models\Hiring\HiringStageEvaluation;
use App\Services\Administration\Hiring\HiringService;

class StageEvaluationForm extends Component
{
    use WithFileUploads;

    public $candidateId;
    public $stageId;
    public $assignedTo;
    public $status = 'pending';
    public $notes = '';
    public $feedback = '';
    public $rating;
    public $files = [];

    public $candidate;
    public $stage;
    public $evaluation;
    public $evaluators;

    protected $hiringService;

    public function boot(HiringService $hiringService)
    {
        $this->hiringService = $hiringService;
    }

    public function mount($candidateId, $stageId)
    {
        $this->candidateId = $candidateId;
        $this->stageId = $stageId;

        $this->candidate = HiringCandidate::findOrFail($candidateId);
        $this->stage = HiringStage::findOrFail($stageId);
        $this->evaluators = $this->hiringService->getAvailableEvaluators();

        // Load existing evaluation if exists
        $this->evaluation = HiringStageEvaluation::where('hiring_candidate_id', $candidateId)
            ->where('hiring_stage_id', $stageId)
            ->first();

        if ($this->evaluation) {
            $this->assignedTo = $this->evaluation->assigned_to;
            $this->status = $this->evaluation->status;
            $this->notes = $this->evaluation->notes ?? '';
            $this->feedback = $this->evaluation->feedback ?? '';
            $this->rating = $this->evaluation->rating;
        } else {
            $this->assignedTo = auth()->id(); // Default to current user
        }
    }

    protected function rules()
    {
        return [
            'assignedTo' => 'required|exists:users,id',
            'status' => 'required|in:pending,in_progress,completed,passed,failed',
            'notes' => 'nullable|string|max:2000',
            'feedback' => 'nullable|string|max:2000',
            'rating' => 'nullable|integer|min:1|max:10',
            'files.*' => 'nullable|file|mimes:pdf,doc,docx,jpg,jpeg,png|max:5120',
        ];
    }

    protected function messages()
    {
        return [
            'assignedTo.required' => 'Please select an evaluator.',
            'assignedTo.exists' => 'Selected evaluator is invalid.',
            'status.required' => 'Status is required.',
            'status.in' => 'Invalid status selected.',
            'notes.max' => 'Notes cannot exceed 2000 characters.',
            'feedback.max' => 'Feedback cannot exceed 2000 characters.',
            'rating.integer' => 'Rating must be a number.',
            'rating.min' => 'Rating must be at least 1.',
            'rating.max' => 'Rating cannot exceed 10.',
            'files.*.file' => 'Each file must be a valid file.',
            'files.*.mimes' => 'Files must be PDF, DOC, DOCX, JPG, JPEG, or PNG.',
            'files.*.max' => 'Each file cannot exceed 5MB.',
        ];
    }

    public function save()
    {
        $this->validate();

        try {
            $data = [
                'hiring_candidate_id' => $this->candidateId,
                'hiring_stage_id' => $this->stageId,
                'assigned_to' => $this->assignedTo,
                'status' => $this->status,
                'notes' => $this->notes,
                'feedback' => $this->feedback,
                'rating' => $this->rating,
            ];

            $evaluation = $this->hiringService->storeOrUpdateEvaluation($data);

            // Handle file uploads
            if (!empty($this->files)) {
                foreach ($this->files as $file) {
                    $directory = 'hiring/evaluations/' . $evaluation->id;
                    store_file_media($file, $evaluation, $directory);
                }
                $this->files = []; // Clear files after upload
            }

            // Auto-progress candidate if evaluation passed
            if ($this->status === 'passed') {
                $this->hiringService->progressToNextStage($this->candidate);
            }

            $this->dispatch('evaluation-saved', [
                'message' => 'Evaluation saved successfully!',
                'type' => 'success'
            ]);

            // Refresh the evaluation
            $this->evaluation = $evaluation->fresh();

        } catch (\Exception $e) {
            $this->dispatch('evaluation-error', [
                'message' => 'Error saving evaluation: ' . $e->getMessage(),
                'type' => 'error'
            ]);
        }
    }

    public function removeFile($index)
    {
        unset($this->files[$index]);
        $this->files = array_values($this->files);
    }

    public function render()
    {
        return view('livewire.administration.hiring.stage-evaluation-form');
    }
}
