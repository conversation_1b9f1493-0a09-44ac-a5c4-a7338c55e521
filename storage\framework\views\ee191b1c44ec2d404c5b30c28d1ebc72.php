<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Edit Candidate')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/dropzone/dropzone.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        .dropzone {
            border: 2px dashed #d9dee3;
            border-radius: 8px;
            background: #f8f9fa;
            min-height: 120px;
        }
        .dropzone.dz-drag-hover {
            border-color: #696cff;
            background: #f3f4ff;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Edit Candidate')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.hiring.index')); ?>"><?php echo e(__('Employee Hiring')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.hiring.show', $hiring_candidate)); ?>"><?php echo e($hiring_candidate->name); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Edit')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<div class="row justify-content-center">
    <div class="col-xl-8 col-lg-10">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo e(__('Edit Candidate Information')); ?></h5>
                <a href="<?php echo e(route('administration.hiring.show', $hiring_candidate)); ?>" class="btn btn-outline-secondary btn-sm">
                    <i class="ti ti-arrow-left"></i> <?php echo e(__('Back to Details')); ?>

                </a>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('administration.hiring.update', $hiring_candidate)); ?>" method="POST" enctype="multipart/form-data" id="candidateForm">
                    <?php echo csrf_field(); ?>
                    <?php echo method_field('PUT'); ?>

                    <div class="row g-3">
                        <!-- Basic Information -->
                        <div class="col-12">
                            <h6 class="text-primary mb-3"><?php echo e(__('Basic Information')); ?></h6>
                        </div>

                        <div class="col-md-6">
                            <label for="name" class="form-label"><?php echo e(__('Full Name')); ?> <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="name" 
                                   name="name" 
                                   value="<?php echo e(old('name', $hiring_candidate->name)); ?>" 
                                   required>
                            <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6">
                            <label for="email" class="form-label"><?php echo e(__('Email Address')); ?> <span class="text-danger">*</span></label>
                            <input type="email" 
                                   class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo e(old('email', $hiring_candidate->email)); ?>" 
                                   required>
                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6">
                            <label for="phone" class="form-label"><?php echo e(__('Phone Number')); ?> <span class="text-danger">*</span></label>
                            <input type="tel" 
                                   class="form-control <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="phone" 
                                   name="phone" 
                                   value="<?php echo e(old('phone', $hiring_candidate->phone)); ?>" 
                                   required>
                            <?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6">
                            <label for="expected_role" class="form-label"><?php echo e(__('Expected Role')); ?> <span class="text-danger">*</span></label>
                            <input type="text" 
                                   class="form-control <?php $__errorArgs = ['expected_role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                   id="expected_role" 
                                   name="expected_role" 
                                   value="<?php echo e(old('expected_role', $hiring_candidate->expected_role)); ?>" 
                                   required>
                            <?php $__errorArgs = ['expected_role'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="col-md-6">
                            <label for="expected_salary" class="form-label"><?php echo e(__('Expected Salary')); ?></label>
                            <div class="input-group">
                                <span class="input-group-text">₹</span>
                                <input type="number" 
                                       class="form-control <?php $__errorArgs = ['expected_salary'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                       id="expected_salary" 
                                       name="expected_salary" 
                                       value="<?php echo e(old('expected_salary', $hiring_candidate->expected_salary)); ?>" 
                                       step="0.01" 
                                       min="0">
                                <?php $__errorArgs = ['expected_salary'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <div class="invalid-feedback"><?php echo e($message); ?></div>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <label for="status" class="form-label"><?php echo e(__('Status')); ?> <span class="text-danger">*</span></label>
                            <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                    id="status" 
                                    name="status" 
                                    required>
                                <?php $__currentLoopData = \App\Models\Hiring\HiringCandidate::getStatuses(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <option value="<?php echo e($key); ?>" <?php echo e(old('status', $hiring_candidate->status) == $key ? 'selected' : ''); ?>>
                                        <?php echo e($label); ?>

                                    </option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Notes -->
                        <div class="col-12">
                            <label for="notes" class="form-label"><?php echo e(__('Notes')); ?></label>
                            <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                      id="notes" 
                                      name="notes" 
                                      rows="3" 
                                      placeholder="<?php echo e(__('Any additional notes about the candidate...')); ?>"><?php echo e(old('notes', $hiring_candidate->notes)); ?></textarea>
                            <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="invalid-feedback"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- File Uploads -->
                        <div class="col-12">
                            <h6 class="text-primary mb-3 mt-4"><?php echo e(__('Additional Documents')); ?></h6>
                        </div>

                        <div class="col-12">
                            <label for="files" class="form-label"><?php echo e(__('Upload Additional Documents')); ?></label>
                            <div class="dropzone" id="additional-files-dropzone">
                                <div class="dz-message">
                                    <div class="text-center">
                                        <i class="ti ti-cloud-upload display-4 text-muted mb-2"></i>
                                        <h6><?php echo e(__('Drop files here or click to upload')); ?></h6>
                                        <small class="text-muted"><?php echo e(__('PDF, DOC, DOCX, JPG, JPEG, PNG - Max 5MB each')); ?></small>
                                    </div>
                                </div>
                            </div>
                            <?php $__errorArgs = ['files.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <div class="text-danger mt-1"><?php echo e($message); ?></div>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <!-- Existing Files -->
                        <?php if($hiring_candidate->files->count() > 0): ?>
                            <div class="col-12">
                                <h6 class="text-muted mb-3"><?php echo e(__('Existing Documents')); ?></h6>
                                <div class="row g-2">
                                    <?php $__currentLoopData = $hiring_candidate->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="col-md-6">
                                            <div class="card">
                                                <div class="card-body p-3">
                                                    <div class="d-flex align-items-center">
                                                        <i class="ti ti-file-text me-2"></i>
                                                        <div class="flex-grow-1">
                                                            <a href="<?php echo e(Storage::url($file->file_path)); ?>" target="_blank" class="text-decoration-none">
                                                                <?php echo e($file->file_name); ?>

                                                            </a>
                                                            <?php if($file->note): ?>
                                                                <small class="text-muted d-block"><?php echo e($file->note); ?></small>
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        <?php endif; ?>

                        <!-- Submit Buttons -->
                        <div class="col-12">
                            <hr class="my-4">
                            <div class="d-flex justify-content-end gap-3">
                                <a href="<?php echo e(route('administration.hiring.show', $hiring_candidate)); ?>" class="btn btn-outline-secondary">
                                    <?php echo e(__('Cancel')); ?>

                                </a>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-device-floppy"></i> <?php echo e(__('Update Candidate')); ?>

                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/vendor/libs/dropzone/dropzone.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            // Initialize Dropzone for additional files
            Dropzone.autoDiscover = false;
            
            const additionalFilesDropzone = new Dropzone("#additional-files-dropzone", {
                url: "#", // We'll handle this via form submission
                autoProcessQueue: false,
                uploadMultiple: true,
                parallelUploads: 10,
                maxFiles: 10,
                maxFilesize: 5, // MB
                acceptedFiles: ".pdf,.doc,.docx,.jpg,.jpeg,.png",
                addRemoveLinks: true,
                paramName: "files",
                
                init: function() {
                    const dropzone = this;
                    
                    // Handle form submission
                    $("#candidateForm").on("submit", function(e) {
                        if (dropzone.getQueuedFiles().length > 0) {
                            e.preventDefault();
                            
                            // Add files to form data
                            const formData = new FormData(this);
                            dropzone.getQueuedFiles().forEach(function(file, index) {
                                formData.append('files[]', file);
                            });
                            
                            // Submit form with files
                            fetch(this.action, {
                                method: 'POST',
                                body: formData,
                                headers: {
                                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                                }
                            }).then(response => {
                                if (response.ok) {
                                    window.location.href = response.url;
                                } else {
                                    response.text().then(text => {
                                        document.body.innerHTML = text;
                                    });
                                }
                            }).catch(error => {
                                console.error('Error:', error);
                                alert('An error occurred while submitting the form.');
                            });
                        }
                    });
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/hiring/edit.blade.php ENDPATH**/ ?>