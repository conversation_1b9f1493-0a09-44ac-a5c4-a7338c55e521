{"__meta": {"id": "01K1TKP26BMXYA4ETB53AVWWHT", "datetime": "2025-08-04 19:17:39", "utime": **********.91813, "method": "POST", "uri": "/hiring/store", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1754313453.921223, "end": **********.918162, "duration": 5.996939182281494, "duration_str": "6s", "measures": [{"label": "Booting", "start": 1754313453.921223, "relative_start": 0, "end": **********.828461, "relative_end": **********.828461, "duration": 0.****************, "duration_str": "907ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.828486, "relative_start": 0.****************, "end": **********.918165, "relative_end": 2.86102294921875e-06, "duration": 5.***************, "duration_str": "5.09s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.858187, "relative_start": 0.****************, "end": **********.866786, "relative_end": **********.866786, "duration": 0.008599042892456055, "duration_str": "8.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.906544, "relative_start": 5.***************, "end": **********.908111, "relative_end": **********.908111, "duration": 0.0015671253204345703, "duration_str": "1.57ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST hiring/store", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Employee Hiring Create", "controller": "App\\Http\\Controllers\\Administration\\Hiring\\HiringController@store<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "administration.hiring.store", "prefix": "/hiring", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Hiring/HiringController.php:51-90</a>"}, "queries": {"count": 24, "nb_statements": 22, "nb_visible_statements": 24, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.10897000000000001, "accumulated_duration_str": "109ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `users` where `id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/SessionGuard.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\SessionGuard.php", "line": 159}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 19, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.949154, "duration": 0.00583, "duration_str": "5.83ms", "memory": 0, "memory_str": null, "filename": "EloquentUserProvider.php:59", "source": {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/EloquentUserProvider.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\EloquentUserProvider.php", "line": 59}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FAuth%2FEloquentUserProvider.php&line=59", "ajax": false, "filename": "EloquentUserProvider.php", "line": "59"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 5.35}, {"sql": "select `value` from `settings` where `key` = 'unrestricted_users' limit 1", "type": "query", "params": [], "bindings": ["unrestricted_users"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, {"index": 18, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 20}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 20, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.970781, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "unrestricted.users:34", "source": {"index": 17, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 34}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FUnrestrictedUser.php&line=34", "ajax": false, "filename": "UnrestrictedUser.php", "line": "34"}, "connection": "blueorange", "explain": null, "start_percent": 5.35, "width_percent": 0.652}, {"sql": "select `value` from `settings` where `key` = 'mobile_restriction' limit 1", "type": "query", "params": [], "bindings": ["mobile_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.977888, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:31", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=31", "ajax": false, "filename": "RestrictDevices.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 6.002, "width_percent": 0.642}, {"sql": "select `value` from `settings` where `key` = 'computer_restriction' limit 1", "type": "query", "params": [], "bindings": ["computer_restriction"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "localization", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php", "line": 20}], "start": **********.981154, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "restrict.devices:32", "source": {"index": 17, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 32}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictDevices.php&line=32", "ajax": false, "filename": "RestrictDevices.php", "line": "32"}, "connection": "blueorange", "explain": null, "start_percent": 6.644, "width_percent": 0.661}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` = 1 and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, {"index": 26, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 34}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 28, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}], "start": **********.994821, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "UserAccessors.php:36", "source": {"index": 20, "namespace": null, "name": "app/Models/User/Accessors/UserAccessors.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\User\\Accessors\\UserAccessors.php", "line": 36}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FAccessors%2FUserAccessors.php&line=36", "ajax": false, "filename": "UserAccessors.php", "line": "36"}, "connection": "blueorange", "explain": null, "start_percent": 7.305, "width_percent": 0.89}, {"sql": "select `value` from `settings` where `key` = 'allowed_ip_ranges' limit 1", "type": "query", "params": [], "bindings": ["allowed_ip_ranges"], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 19, "namespace": "middleware", "name": "restrict.devices", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php", "line": 46}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 21, "namespace": "middleware", "name": "unrestricted.users", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php", "line": 25}], "start": 1754313455.000219, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "restrict.ip:25", "source": {"index": 17, "namespace": "middleware", "name": "restrict.ip", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php", "line": 25}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FMiddleware%2FRestrictIpRange.php&line=25", "ajax": false, "filename": "RestrictIpRange.php", "line": "25"}, "connection": "blueorange", "explain": null, "start_percent": 8.195, "width_percent": 0.633}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": 1754313455.020759, "duration": 0.00158, "duration_str": "1.58ms", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "blueorange", "explain": null, "start_percent": 8.828, "width_percent": 1.45}, {"sql": "select count(*) as aggregate from `hiring_candidates` where `email` = '<EMAIL>'", "type": "query", "params": [], "bindings": ["<EMAIL>"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Concerns/ValidatesAttributes.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Concerns\\ValidatesAttributes.php", "line": 948}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 660}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 459}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/Validator.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\Validator.php", "line": 494}], "start": 1754313455.971952, "duration": 0.00443, "duration_str": "4.43ms", "memory": 0, "memory_str": null, "filename": "DatabasePresenceVerifier.php:54", "source": {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Validation/DatabasePresenceVerifier.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Validation\\DatabasePresenceVerifier.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FValidation%2FDatabasePresenceVerifier.php&line=54", "ajax": false, "filename": "DatabasePresenceVerifier.php", "line": "54"}, "connection": "blueorange", "explain": null, "start_percent": 10.278, "width_percent": 4.065}, {"sql": "Begin Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.29756, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "HiringController.php:54", "source": {"index": 10, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=54", "ajax": false, "filename": "HiringController.php", "line": "54"}, "connection": "blueorange", "explain": null, "start_percent": 14.343, "width_percent": 0}, {"sql": "insert into `hiring_candidates` (`name`, `email`, `phone`, `expected_role`, `expected_salary`, `notes`, `status`, `current_stage`, `created_by`, `updated_at`, `created_at`) values ('Nigel', '<EMAIL>', '****** 434-7576', 'Ipsum Hic Et Consequ', '25000', 'Fugiat eligendi inv', 'shortlisted', 1, 1, '2025-08-04 19:17:36', '2025-08-04 19:17:36')", "type": "query", "params": [], "bindings": ["<PERSON>", "<EMAIL>", "****** 434-7576", "Ipsum Hic Et Consequ", "25000", "Fugiat eligendi inv", "shortlisted", 1, 1, "2025-08-04 19:17:36", "2025-08-04 19:17:36"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Services/Administration/Hiring/HiringService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Hiring\\HiringService.php", "line": 22}, {"index": 22, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 55}, {"index": 26, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.403146, "duration": 0.0024700000000000004, "duration_str": "2.47ms", "memory": 0, "memory_str": null, "filename": "HiringService.php:22", "source": {"index": 21, "namespace": null, "name": "app/Services/Administration/Hiring/HiringService.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Hiring\\HiringService.php", "line": 22}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FServices%2FAdministration%2FHiring%2FHiringService.php&line=22", "ajax": false, "filename": "HiringService.php", "line": "22"}, "connection": "blueorange", "explain": null, "start_percent": 14.343, "width_percent": 2.267}, {"sql": "insert into `file_media` (`file_name`, `file_path`, `mime_type`, `file_size`, `original_name`, `note`, `fileable_id`, `fileable_type`, `uploader_id`, `updated_at`, `created_at`) values ('<PERSON><PERSON><PERSON> (<PERSON>).pdf', 'hiring/candidates/1/resume/aPLZvxWOq9ixZiDg3qkvjrngIJSBJPtrVYgaFFTV.pdf', 'application/pdf', 8224, '<PERSON><PERSON><PERSON> (<PERSON>).pdf', 'Resume', 1, 'App\\\\Models\\\\Hiring\\\\HiringCandidate', 1, '2025-08-04 19:17:36', '2025-08-04 19:17:36')", "type": "query", "params": [], "bindings": ["<PERSON><PERSON><PERSON> (Neil).pdf", "hiring/candidates/1/resume/aPLZvxWOq9ixZiDg3qkvjrngIJSBJPtrVYgaFFTV.pdf", "application/pdf", 8224, "<PERSON><PERSON><PERSON> (Neil).pdf", "Resume", 1, "App\\Models\\Hiring\\HiringCandidate", 1, "2025-08-04 19:17:36", "2025-08-04 19:17:36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.616459, "duration": 0.027469999999999998, "duration_str": "27.47ms", "memory": 0, "memory_str": null, "filename": "FileMediaHelper.php:30", "source": {"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FFileMediaHelper.php&line=30", "ajax": false, "filename": "FileMediaHelper.php", "line": "30"}, "connection": "blueorange", "explain": null, "start_percent": 16.61, "width_percent": 25.209}, {"sql": "insert into `file_media` (`file_name`, `file_path`, `mime_type`, `file_size`, `original_name`, `note`, `fileable_id`, `fileable_type`, `uploader_id`, `updated_at`, `created_at`) values ('employment_certificate.png', 'hiring/candidates/1/documents/MUdJNw1DF3zQDovQpMVzUSGQdbkBEaB22exdzcCn.png', 'image/png', 131870, 'employment_certificate.png', null, 1, 'App\\\\Models\\\\Hiring\\\\HiringCandidate', 1, '2025-08-04 19:17:36', '2025-08-04 19:17:36')", "type": "query", "params": [], "bindings": ["employment_certificate.png", "hiring/candidates/1/documents/MUdJNw1DF3zQDovQpMVzUSGQdbkBEaB22exdzcCn.png", "image/png", 131870, "employment_certificate.png", null, 1, "App\\Models\\Hiring\\HiringCandidate", 1, "2025-08-04 19:17:36", "2025-08-04 19:17:36"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.656617, "duration": 0.00107, "duration_str": "1.07ms", "memory": 0, "memory_str": null, "filename": "FileMediaHelper.php:30", "source": {"index": 16, "namespace": null, "name": "app/Helpers/FileMediaHelper.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Helpers\\FileMediaHelper.php", "line": 30}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHelpers%2FFileMediaHelper.php&line=30", "ajax": false, "filename": "FileMediaHelper.php", "line": "30"}, "connection": "blueorange", "explain": null, "start_percent": 41.819, "width_percent": 0.982}, {"sql": "select * from `hiring_stages` where `stage_order` = 1 and `hiring_stages`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 72}, {"index": 20, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.663089, "duration": 0.00146, "duration_str": "1.46ms", "memory": 0, "memory_str": null, "filename": "HiringController.php:72", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 72}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=72", "ajax": false, "filename": "HiringController.php", "line": "72"}, "connection": "blueorange", "explain": null, "start_percent": 42.801, "width_percent": 1.34}, {"sql": "insert into `hiring_stage_evaluations` (`hiring_candidate_id`, `hiring_stage_id`, `assigned_to`, `status`, `created_by`, `assigned_at`, `updated_at`, `created_at`) values (1, 1, 1, 'pending', 1, '2025-08-04 19:17:36', '2025-08-04 19:17:36', '2025-08-04 19:17:36')", "type": "query", "params": [], "bindings": [1, 1, 1, "pending", 1, "2025-08-04 19:17:36", "2025-08-04 19:17:36", "2025-08-04 19:17:36"], "hints": null, "show_copy": false, "backtrace": [{"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 74}, {"index": 25, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 26, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 27, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 28, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.67238, "duration": 0.00148, "duration_str": "1.48ms", "memory": 0, "memory_str": null, "filename": "HiringController.php:74", "source": {"index": 21, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 74}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=74", "ajax": false, "filename": "HiringController.php", "line": "74"}, "connection": "blueorange", "explain": null, "start_percent": 44.141, "width_percent": 1.358}, {"sql": "select * from `users` where `users`.`id` in (1) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 20, "namespace": null, "name": "app/Observers/Administration/Hiring/HiringCandidateObserver.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Observers\\Administration\\Hiring\\HiringCandidateObserver.php", "line": 18}, {"index": 32, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 33, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 34, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 35, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.682337, "duration": 0.00116, "duration_str": "1.16ms", "memory": 0, "memory_str": null, "filename": "HiringCandidateObserver.php:18", "source": {"index": 20, "namespace": null, "name": "app/Observers/Administration/Hiring/HiringCandidateObserver.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Observers\\Administration\\Hiring\\HiringCandidateObserver.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FObservers%2FAdministration%2FHiring%2FHiringCandidateObserver.php&line=18", "ajax": false, "filename": "HiringCandidateObserver.php", "line": "18"}, "connection": "blueorange", "explain": null, "start_percent": 45.499, "width_percent": 1.065}, {"sql": "select * from `employees` where `employees`.`user_id` in (1) and `employees`.`deleted_at` is null", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 25, "namespace": null, "name": "app/Observers/Administration/Hiring/HiringCandidateObserver.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Observers\\Administration\\Hiring\\HiringCandidateObserver.php", "line": 18}, {"index": 37, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 38, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 39, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 40, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.690373, "duration": 0.0024300000000000003, "duration_str": "2.43ms", "memory": 0, "memory_str": null, "filename": "HiringCandidateObserver.php:18", "source": {"index": 25, "namespace": null, "name": "app/Observers/Administration/Hiring/HiringCandidateObserver.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Observers\\Administration\\Hiring\\HiringCandidateObserver.php", "line": 18}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FObservers%2FAdministration%2FHiring%2FHiringCandidateObserver.php&line=18", "ajax": false, "filename": "HiringCandidateObserver.php", "line": "18"}, "connection": "blueorange", "explain": null, "start_percent": 46.563, "width_percent": 2.23}, {"sql": "select * from `users` where `status` = 'Active' and exists (select * from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `users`.`id` = `model_has_roles`.`model_id` and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User' and (`name` in ('Super Admin', 'Developer') or exists (select * from `permissions` inner join `role_has_permissions` on `permissions`.`id` = `role_has_permissions`.`permission_id` where `roles`.`id` = `role_has_permissions`.`role_id` and `name` like 'Employee Hiring%'))) and `users`.`deleted_at` is null", "type": "query", "params": [], "bindings": ["Active", "App\\Models\\User", "Super Admin", "Developer", "Employee Hiring%"], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Observers/Administration/Hiring/HiringCandidateObserver.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Observers\\Administration\\Hiring\\HiringCandidateObserver.php", "line": 73}, {"index": 16, "namespace": null, "name": "app/Observers/Administration/Hiring/HiringCandidateObserver.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Observers\\Administration\\Hiring\\HiringCandidateObserver.php", "line": 21}, {"index": 28, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 29, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 30, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.6963701, "duration": 0.01473, "duration_str": "14.73ms", "memory": 0, "memory_str": null, "filename": "HiringCandidateObserver.php:73", "source": {"index": 15, "namespace": null, "name": "app/Observers/Administration/Hiring/HiringCandidateObserver.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Observers\\Administration\\Hiring\\HiringCandidateObserver.php", "line": 73}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FObservers%2FAdministration%2FHiring%2FHiringCandidateObserver.php&line=73", "ajax": false, "filename": "HiringCandidateObserver.php", "line": "73"}, "connection": "blueorange", "explain": null, "start_percent": 48.793, "width_percent": 13.517}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"7d6ef2c8-64ad-49f0-8e81-d9becd3a06ab\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:1;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:74:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\\\\\":4:{s:12:\\\\\\\"\\\\u0000*\\\\u0000candidate\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:33:\\\\\\\"App\\\\\\\\Models\\\\\\\\Hiring\\\\\\\\HiringCandidate\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:7:\\\\\\\"creator\\\\\\\";i:1;s:16:\\\\\\\"creator.employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:9:\\\\\\\"\\\\u0000*\\\\u0000action\\\\\\\";s:7:\\\\\\\"created\\\\\\\";s:11:\\\\\\\"\\\\u0000*\\\\u0000actionBy\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:5:\\\\\\\"roles\\\\\\\";i:1;s:11:\\\\\\\"permissions\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"b5ce127d-8cec-47d7-88ce-2098b5e71f42\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:8:\\\\\\\"database\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"7d6ef2c8-64ad-49f0-8e81-d9becd3a06ab\",\"displayName\":\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueued<PERSON><PERSON>ler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:74:\\\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\\\":4:{s:12:\\\"\\u0000*\\u0000candidate\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:33:\\\"App\\\\Models\\\\Hiring\\\\HiringCandidate\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:7:\\\"creator\\\";i:1;s:16:\\\"creator.employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:9:\\\"\\u0000*\\u0000action\\\";s:7:\\\"created\\\";s:11:\\\"\\u0000*\\u0000actionBy\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:5:\\\"roles\\\";i:1;s:11:\\\"permissions\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"b5ce127d-8cec-47d7-88ce-2098b5e71f42\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.569144, "duration": 0.028399999999999998, "duration_str": "28.4ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 62.311, "width_percent": 26.062}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"07107fc0-9a5a-472a-9b71-c6a30f20d774\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:1;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:74:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\\\\\":4:{s:12:\\\\\\\"\\\\u0000*\\\\u0000candidate\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:33:\\\\\\\"App\\\\\\\\Models\\\\\\\\Hiring\\\\\\\\HiringCandidate\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:7:\\\\\\\"creator\\\\\\\";i:1;s:16:\\\\\\\"creator.employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:9:\\\\\\\"\\\\u0000*\\\\u0000action\\\\\\\";s:7:\\\\\\\"created\\\\\\\";s:11:\\\\\\\"\\\\u0000*\\\\u0000actionBy\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:5:\\\\\\\"roles\\\\\\\";i:1;s:11:\\\\\\\"permissions\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"b5ce127d-8cec-47d7-88ce-2098b5e71f42\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:4:\\\\\\\"mail\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"07107fc0-9a5a-472a-9b71-c6a30f20d774\",\"displayName\":\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedH<PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:1;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:74:\\\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\\\":4:{s:12:\\\"\\u0000*\\u0000candidate\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:33:\\\"App\\\\Models\\\\Hiring\\\\HiringCandidate\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:7:\\\"creator\\\";i:1;s:16:\\\"creator.employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:9:\\\"\\u0000*\\u0000action\\\";s:7:\\\"created\\\";s:11:\\\"\\u0000*\\u0000actionBy\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:5:\\\"roles\\\";i:1;s:11:\\\"permissions\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"b5ce127d-8cec-47d7-88ce-2098b5e71f42\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.640995, "duration": 0.00235, "duration_str": "2.35ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 88.373, "width_percent": 2.157}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"021e4489-63f6-47e3-bd1d-985a5cb631eb\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:2;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:74:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\\\\\":4:{s:12:\\\\\\\"\\\\u0000*\\\\u0000candidate\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:33:\\\\\\\"App\\\\\\\\Models\\\\\\\\Hiring\\\\\\\\HiringCandidate\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:7:\\\\\\\"creator\\\\\\\";i:1;s:16:\\\\\\\"creator.employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:9:\\\\\\\"\\\\u0000*\\\\u0000action\\\\\\\";s:7:\\\\\\\"created\\\\\\\";s:11:\\\\\\\"\\\\u0000*\\\\u0000actionBy\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:5:\\\\\\\"roles\\\\\\\";i:1;s:11:\\\\\\\"permissions\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"d7e57c4d-e49f-4f51-a515-3db2a55e03ae\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:8:\\\\\\\"database\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"021e4489-63f6-47e3-bd1d-985a5cb631eb\",\"displayName\":\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:2;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:74:\\\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\\\":4:{s:12:\\\"\\u0000*\\u0000candidate\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:33:\\\"App\\\\Models\\\\Hiring\\\\HiringCandidate\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:7:\\\"creator\\\";i:1;s:16:\\\"creator.employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:9:\\\"\\u0000*\\u0000action\\\";s:7:\\\"created\\\";s:11:\\\"\\u0000*\\u0000actionBy\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:5:\\\"roles\\\";i:1;s:11:\\\"permissions\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"d7e57c4d-e49f-4f51-a515-3db2a55e03ae\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.646694, "duration": 0.00252, "duration_str": "2.52ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 90.53, "width_percent": 2.313}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"09b1b630-84a7-45f9-a369-0d2988abb56c\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:2;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:74:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\\\\\":4:{s:12:\\\\\\\"\\\\u0000*\\\\u0000candidate\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:33:\\\\\\\"App\\\\\\\\Models\\\\\\\\Hiring\\\\\\\\HiringCandidate\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:7:\\\\\\\"creator\\\\\\\";i:1;s:16:\\\\\\\"creator.employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:9:\\\\\\\"\\\\u0000*\\\\u0000action\\\\\\\";s:7:\\\\\\\"created\\\\\\\";s:11:\\\\\\\"\\\\u0000*\\\\u0000actionBy\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:5:\\\\\\\"roles\\\\\\\";i:1;s:11:\\\\\\\"permissions\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"d7e57c4d-e49f-4f51-a515-3db2a55e03ae\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:4:\\\\\\\"mail\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"09b1b630-84a7-45f9-a369-0d2988abb56c\",\"displayName\":\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:2;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:74:\\\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\\\":4:{s:12:\\\"\\u0000*\\u0000candidate\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:33:\\\"App\\\\Models\\\\Hiring\\\\HiringCandidate\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:7:\\\"creator\\\";i:1;s:16:\\\"creator.employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:9:\\\"\\u0000*\\u0000action\\\";s:7:\\\"created\\\";s:11:\\\"\\u0000*\\u0000actionBy\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:5:\\\"roles\\\";i:1;s:11:\\\"permissions\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"d7e57c4d-e49f-4f51-a515-3db2a55e03ae\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.6523001, "duration": 0.00225, "duration_str": "2.25ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 92.842, "width_percent": 2.065}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"6f1c8d03-2e1c-4adb-b944-9e82e6c8e1f4\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:3;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:74:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\\\\\":4:{s:12:\\\\\\\"\\\\u0000*\\\\u0000candidate\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:33:\\\\\\\"App\\\\\\\\Models\\\\\\\\Hiring\\\\\\\\HiringCandidate\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:7:\\\\\\\"creator\\\\\\\";i:1;s:16:\\\\\\\"creator.employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:9:\\\\\\\"\\\\u0000*\\\\u0000action\\\\\\\";s:7:\\\\\\\"created\\\\\\\";s:11:\\\\\\\"\\\\u0000*\\\\u0000actionBy\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:5:\\\\\\\"roles\\\\\\\";i:1;s:11:\\\\\\\"permissions\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"fcc21733-5c86-4cfc-bb4d-fb90817b14bc\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:8:\\\\\\\"database\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"6f1c8d03-2e1c-4adb-b944-9e82e6c8e1f4\",\"displayName\":\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueuedHandler@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:3;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:74:\\\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\\\":4:{s:12:\\\"\\u0000*\\u0000candidate\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:33:\\\"App\\\\Models\\\\Hiring\\\\HiringCandidate\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:7:\\\"creator\\\";i:1;s:16:\\\"creator.employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:9:\\\"\\u0000*\\u0000action\\\";s:7:\\\"created\\\";s:11:\\\"\\u0000*\\u0000actionBy\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:5:\\\"roles\\\";i:1;s:11:\\\"permissions\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"fcc21733-5c86-4cfc-bb4d-fb90817b14bc\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:8:\\\"database\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.657767, "duration": 0.00325, "duration_str": "3.25ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 94.907, "width_percent": 2.982}, {"sql": "insert into `jobs` (`queue`, `attempts`, `reserved_at`, `available_at`, `created_at`, `payload`) values ('default', 0, null, **********, **********, '{\\\"uuid\\\":\\\"f34835e3-9e0b-413a-88e9-e38101b55aa4\\\",\\\"displayName\\\":\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\",\\\"job\\\":\\\"Illuminate\\\\\\\\Queue\\\\\\\\CallQueuedHandler@call\\\",\\\"maxTries\\\":null,\\\"maxExceptions\\\":null,\\\"failOnTimeout\\\":false,\\\"backoff\\\":null,\\\"timeout\\\":null,\\\"retryUntil\\\":null,\\\"data\\\":{\\\"commandName\\\":\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\",\\\"command\\\":\\\"O:48:\\\\\\\"Illuminate\\\\\\\\Notifications\\\\\\\\SendQueuedNotifications\\\\\\\":3:{s:11:\\\\\\\"notifiables\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";a:1:{i:0;i:3;}s:9:\\\\\\\"relations\\\\\\\";a:0:{}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:12:\\\\\\\"notification\\\\\\\";O:74:\\\\\\\"App\\\\\\\\Notifications\\\\\\\\Administration\\\\\\\\Hiring\\\\\\\\CandidateStatusChangedNotification\\\\\\\":4:{s:12:\\\\\\\"\\\\u0000*\\\\u0000candidate\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:33:\\\\\\\"App\\\\\\\\Models\\\\\\\\Hiring\\\\\\\\HiringCandidate\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:7:\\\\\\\"creator\\\\\\\";i:1;s:16:\\\\\\\"creator.employee\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:9:\\\\\\\"\\\\u0000*\\\\u0000action\\\\\\\";s:7:\\\\\\\"created\\\\\\\";s:11:\\\\\\\"\\\\u0000*\\\\u0000actionBy\\\\\\\";O:45:\\\\\\\"Illuminate\\\\\\\\Contracts\\\\\\\\Database\\\\\\\\ModelIdentifier\\\\\\\":5:{s:5:\\\\\\\"class\\\\\\\";s:15:\\\\\\\"App\\\\\\\\Models\\\\\\\\User\\\\\\\";s:2:\\\\\\\"id\\\\\\\";i:1;s:9:\\\\\\\"relations\\\\\\\";a:2:{i:0;s:5:\\\\\\\"roles\\\\\\\";i:1;s:11:\\\\\\\"permissions\\\\\\\";}s:10:\\\\\\\"connection\\\\\\\";s:5:\\\\\\\"mysql\\\\\\\";s:15:\\\\\\\"collectionClass\\\\\\\";N;}s:2:\\\\\\\"id\\\\\\\";s:36:\\\\\\\"fcc21733-5c86-4cfc-bb4d-fb90817b14bc\\\\\\\";}s:8:\\\\\\\"channels\\\\\\\";a:1:{i:0;s:4:\\\\\\\"mail\\\\\\\";}}\\\"}}')", "type": "query", "params": [], "bindings": ["default", 0, null, **********, **********, "{\"uuid\":\"f34835e3-9e0b-413a-88e9-e38101b55aa4\",\"displayName\":\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\",\"job\":\"Illuminate\\\\Queue\\\\CallQueued<PERSON><PERSON><PERSON>@call\",\"maxTries\":null,\"maxExceptions\":null,\"failOnTimeout\":false,\"backoff\":null,\"timeout\":null,\"retryUntil\":null,\"data\":{\"commandName\":\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\",\"command\":\"O:48:\\\"Illuminate\\\\Notifications\\\\SendQueuedNotifications\\\":3:{s:11:\\\"notifiables\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";a:1:{i:0;i:3;}s:9:\\\"relations\\\";a:0:{}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:12:\\\"notification\\\";O:74:\\\"App\\\\Notifications\\\\Administration\\\\Hiring\\\\CandidateStatusChangedNotification\\\":4:{s:12:\\\"\\u0000*\\u0000candidate\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:33:\\\"App\\\\Models\\\\Hiring\\\\HiringCandidate\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:7:\\\"creator\\\";i:1;s:16:\\\"creator.employee\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:9:\\\"\\u0000*\\u0000action\\\";s:7:\\\"created\\\";s:11:\\\"\\u0000*\\u0000actionBy\\\";O:45:\\\"Illuminate\\\\Contracts\\\\Database\\\\ModelIdentifier\\\":5:{s:5:\\\"class\\\";s:15:\\\"App\\\\Models\\\\User\\\";s:2:\\\"id\\\";i:1;s:9:\\\"relations\\\";a:2:{i:0;s:5:\\\"roles\\\";i:1;s:11:\\\"permissions\\\";}s:10:\\\"connection\\\";s:5:\\\"mysql\\\";s:15:\\\"collectionClass\\\";N;}s:2:\\\"id\\\";s:36:\\\"fcc21733-5c86-4cfc-bb4d-fb90817b14bc\\\";}s:8:\\\"channels\\\";a:1:{i:0;s:4:\\\"mail\\\";}}\"}}"], "hints": null, "show_copy": false, "backtrace": [{"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 96}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/Queue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\Queue.php", "line": 342}, {"index": 14, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 90}, {"index": 15, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Bus/Dispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Bus\\Dispatcher.php", "line": 254}], "start": **********.664379, "duration": 0.0023, "duration_str": "2.3ms", "memory": 0, "memory_str": null, "filename": "DatabaseQueue.php:185", "source": {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Queue/DatabaseQueue.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Queue\\DatabaseQueue.php", "line": 185}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FQueue%2FDatabaseQueue.php&line=185", "ajax": false, "filename": "DatabaseQueue.php", "line": "185"}, "connection": "blueorange", "explain": null, "start_percent": 97.889, "width_percent": 2.111}, {"sql": "Commit Transaction", "type": "transaction", "params": [], "bindings": [], "hints": null, "show_copy": false, "backtrace": [{"index": 9, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, {"index": 10, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 11, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 12, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 13, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.672607, "duration": 0, "duration_str": "", "memory": 0, "memory_str": null, "filename": "HiringController.php:54", "source": {"index": 9, "namespace": null, "name": "app/Http/Controllers/Administration/Hiring/HiringController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php", "line": 54}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=54", "ajax": false, "filename": "HiringController.php", "line": "54"}, "connection": "blueorange", "explain": null, "start_percent": 100, "width_percent": 0}]}, "models": {"data": {"App\\Models\\User": {"value": 5, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "App\\Models\\Settings\\Settings": {"value": 4, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FSettings%2FSettings.php&line=1", "ajax": false, "filename": "Settings.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}, "App\\Models\\Hiring\\HiringStage": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FHiring%2FHiringStage.php&line=1", "ajax": false, "filename": "HiringStage.php", "line": "?"}}, "App\\Models\\User\\Employee\\Employee": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FUser%2FEmployee%2FEmployee.php&line=1", "ajax": false, "filename": "Employee.php", "line": "?"}}}, "count": 12, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => Employee Hiring Create,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-1769976073 data-indent-pad=\"  \"><span class=sf-dump-note>Employee Hiring Create </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"22 characters\">Employee Hiring Create</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1769976073\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": 1754313455.030922, "xdebug_link": null}]}, "session": {"_token": "FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => array:13 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.padding\"\n    5 => \"alert.config.showConfirmButton\"\n    6 => \"alert.config.showCloseButton\"\n    7 => \"alert.config.timerProgressBar\"\n    8 => \"alert.config.customClass\"\n    9 => \"alert.config.toast\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config.position\"\n    12 => \"alert.config\"\n  ]\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/hiring/create\"\n]", "login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d": "1", "auth": "array:1 [\n  \"password_confirmed_at\" => 1752832853\n]", "alert": "array:1 [\n  \"config\" => \"{\"title\":\"Candidate <PERSON> added successfully.\",\"text\":\"\",\"timer\":\"5000\",\"width\":\"32rem\",\"padding\":\"1.25rem\",\"showConfirmButton\":false,\"showCloseButton\":true,\"timerProgressBar\":true,\"customClass\":{\"container\":null,\"popup\":null,\"header\":null,\"title\":null,\"closeButton\":null,\"icon\":null,\"image\":null,\"content\":null,\"input\":null,\"actions\":null,\"confirmButton\":null,\"cancelButton\":null,\"footer\":null},\"toast\":true,\"icon\":\"success\",\"position\":\"top-end\"}\"\n]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "302 Found", "full_url": "https://blueorange.test/hiring/store", "action_name": "administration.hiring.store", "controller_action": "App\\Http\\Controllers\\Administration\\Hiring\\HiringController@store", "uri": "POST hiring/store", "controller": "App\\Http\\Controllers\\Administration\\Hiring\\HiringController@store<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=51\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "/hiring", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FAdministration%2FHiring%2FHiringController.php&line=51\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Administration/Hiring/HiringController.php:51-90</a>", "middleware": "web, auth, active_user, localization, unrestricted.users, restrict.devices, restrict.ip, can:Employee Hiring Create", "duration": "6.03s", "peak_memory": "38MB", "response": "Redirect to https://blueorange.test/hiring/show/mW4OMlxv05P2L9jp", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1963636878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1963636878\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-720680511 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Nigel</span>\"\n  \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"\n  \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"17 characters\">+****************</span>\"\n  \"<span class=sf-dump-key>expected_role</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ipsum hic et consequ</span>\"\n  \"<span class=sf-dump-key>expected_salary</span>\" => \"<span class=sf-dump-str title=\"5 characters\">25000</span>\"\n  \"<span class=sf-dump-key>notes</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Fugiat eligendi inv</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-720680511\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-624168267 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">141241</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-csrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 OPR/120.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"56 characters\">&quot;Opera&quot;;v=&quot;120&quot;, &quot;Not-A.Brand&quot;;v=&quot;8&quot;, &quot;Chromium&quot;;v=&quot;135&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"68 characters\">multipart/form-data; boundary=----WebKitFormBoundaryf3CVjL1HS2nUk3at</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">https://blueorange.test/hiring/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">en-US,en;q=0.9,bn;q=0.8,hi;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1072 characters\">laravel_session=eyJpdiI6ImxSWlNwRTJBMStCb2FCNnNTblhLL0E9PSIsInZhbHVlIjoiaE9FRldidkllR0VMUTNDdDRmRXNJcFVwY0JzR0t6eTIyL1FodFVqV0d5R09Ody91bXZvSXFXbWQxRC9iOUZlS05oRWpqSFYzd294OTBRUEUrOVpyckxqQnU2a3dnUGd4cmprcVAvcDNzbkhWQ0pxc1h1OTJXV3J2VlNwcmZyeTAiLCJtYWMiOiI1ODVjZDkzOWMyYmZjZDI0MDQ1ZGU4ZjNmMmZmMGRhMDFlOTRhMWEyMDEzNzIyZmU2NTM4NzA0ZWUyZDNkODczIiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkFCT1NtWGRRcHdzQWNYSStoZnV1bnc9PSIsInZhbHVlIjoiQnRYYklHNjFvM2Jjc045SHdOQ0FjSUY5NGl6Y1lGT29QcnV0TkZHN1c1ZkFVVEpoV0tpaE43N1JWMStzNk8vZE1KeEp4T0tDQk0xMzVIN1NFVngybVVyS3gvRFF0bzBCTTQ0cmRDRHk0RTkxYTRoRDJJaTRjRG1odm5udkNsZHEiLCJtYWMiOiI3NzkwMDNmZmQ2NjVhOTg2MjU0Y2U1MTM2NmM1OTk1MTdhZmNmMTFlN2ZjMGM5MDcxZjBkMjUxYzVkNTQ1MzJjIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6Imo5Vy96NVJWdWw3ZUo5c3hDQ240UUE9PSIsInZhbHVlIjoiOFVKQUUxWEh1d1dXclMvVlRtbVc0bGJYMjZvUk1md0dQWDlBVEl0V25nRFhFVzA5T2VjblE2Mm82b0JYTHptbGwzbUdZczVNOVZCajFrTWtDZHpmVWo1c3hIZnc2WEdIV0xmU3NWK1FkbDYvemg1VFUzVkM4VDkyV3RHZmVjUUwiLCJtYWMiOiI3NzQ3MzNjNDZkZjc2ZDE2ZTllNTJjN2YyMDIxMTc0M2Q0MjRjYmRlNmExYjE3NTJlMzgyOGJiOWJmMzNkN2Q5IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-624168267\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-643468385 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">6D0Nrao6YaZzbpZmCPN6TqkHFLLTVyDLHkZvE0DI</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">pU9hKUo8erm4hsoGsDajoymGto9RQabaIruS2lk5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-643468385\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-684905399 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 04 Aug 2025 13:17:39 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>location</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"52 characters\">https://blueorange.test/hiring/show/mW4OMlxv05P2L9jp</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=utf-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-684905399\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-986884613 data-indent-pad=\"  \"><span class=sf-dump-note>array:7</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">FPwvnCo4ipPN6QTg3aaTJdnMmsfub9OhTAL7JpDq</span>\"\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:13</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.toast</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"21 characters\">alert.config.position</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"37 characters\">https://blueorange.test/hiring/create</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>login_web_59ba36addc2b2f9401580f014c7f58ea4e30989d</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>auth</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>password_confirmed_at</span>\" => <span class=sf-dump-num>1752832853</span>\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>config</span>\" => \"<span class=sf-dump-str title=\"447 characters\">{&quot;title&quot;:&quot;Candidate Nigel added successfully.&quot;,&quot;text&quot;:&quot;&quot;,&quot;timer&quot;:&quot;5000&quot;,&quot;width&quot;:&quot;32rem&quot;,&quot;padding&quot;:&quot;1.25rem&quot;,&quot;showConfirmButton&quot;:false,&quot;showCloseButton&quot;:true,&quot;timerProgressBar&quot;:true,&quot;customClass&quot;:{&quot;container&quot;:null,&quot;popup&quot;:null,&quot;header&quot;:null,&quot;title&quot;:null,&quot;closeButton&quot;:null,&quot;icon&quot;:null,&quot;image&quot;:null,&quot;content&quot;:null,&quot;input&quot;:null,&quot;actions&quot;:null,&quot;confirmButton&quot;:null,&quot;cancelButton&quot;:null,&quot;footer&quot;:null},&quot;toast&quot;:true,&quot;icon&quot;:&quot;success&quot;,&quot;position&quot;:&quot;top-end&quot;}</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-986884613\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "302 Found", "full_url": "https://blueorange.test/hiring/store", "action_name": "administration.hiring.store", "controller_action": "App\\Http\\Controllers\\Administration\\Hiring\\HiringController@store"}, "badge": "302 Found"}}