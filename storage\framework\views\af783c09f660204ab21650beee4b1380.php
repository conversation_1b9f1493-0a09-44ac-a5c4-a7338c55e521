<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('My Evaluations')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        .evaluation-card {
            transition: all 0.3s ease;
            border-left: 4px solid #e9ecef;
        }
        .evaluation-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
        }
        .evaluation-card.status-pending {
            border-left-color: #6c757d;
        }
        .evaluation-card.status-in-progress {
            border-left-color: #ffc107;
        }
        .evaluation-card.status-completed {
            border-left-color: #17a2b8;
        }
        .evaluation-card.status-passed {
            border-left-color: #28a745;
        }
        .evaluation-card.status-failed {
            border-left-color: #dc3545;
        }
        .stage-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('My Evaluations')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.hiring.index')); ?>"><?php echo e(__('Employee Hiring')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('My Evaluations')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0"><?php echo e(__('My Assigned Evaluations')); ?></h4>
                <p class="text-muted mb-0"><?php echo e(__('Candidates assigned to you for evaluation')); ?></p>
            </div>
            <div>
                <a href="<?php echo e(route('administration.hiring.index')); ?>" class="btn btn-outline-primary">
                    <i class="ti ti-arrow-left"></i> <?php echo e(__('Back to All Candidates')); ?>

                </a>
            </div>
        </div>
    </div>
</div>

<!-- Evaluations List -->
<div class="row">
    <?php $__empty_1 = true; $__currentLoopData = $evaluations; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $evaluation): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-xl-6 col-lg-8 col-md-12 mb-4">
            <div class="card evaluation-card h-100 status-<?php echo e($evaluation->status); ?>">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5 class="card-title mb-1"><?php echo e($evaluation->candidate->name); ?></h5>
                            <p class="text-muted mb-0"><?php echo e($evaluation->candidate->expected_role); ?></p>
                        </div>
                        <div class="text-end">
                            <span class="badge stage-badge bg-primary">
                                <?php echo e($evaluation->stage->name); ?>

                            </span>
                            <br>
                            <span class="badge <?php echo e($evaluation->status_badge_class); ?> mt-1">
                                <?php echo e($evaluation->status_formatted); ?>

                            </span>
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="row g-2">
                            <div class="col-6">
                                <small class="text-muted d-block"><?php echo e(__('Candidate Email')); ?></small>
                                <strong><?php echo e($evaluation->candidate->email); ?></strong>
                            </div>
                            <div class="col-6">
                                <small class="text-muted d-block"><?php echo e(__('Phone')); ?></small>
                                <strong><?php echo e($evaluation->candidate->phone); ?></strong>
                            </div>
                            <?php if($evaluation->candidate->expected_salary): ?>
                                <div class="col-6">
                                    <small class="text-muted d-block"><?php echo e(__('Expected Salary')); ?></small>
                                    <strong><?php echo e($evaluation->candidate->expected_salary_formatted); ?></strong>
                                </div>
                            <?php endif; ?>
                            <?php if($evaluation->rating): ?>
                                <div class="col-6">
                                    <small class="text-muted d-block"><?php echo e(__('My Rating')); ?></small>
                                    <strong><?php echo e($evaluation->rating); ?>/10</strong>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <?php if($evaluation->notes || $evaluation->feedback): ?>
                        <div class="mb-3">
                            <?php if($evaluation->notes): ?>
                                <div class="mb-2">
                                    <small class="text-muted d-block"><?php echo e(__('My Notes')); ?></small>
                                    <p class="mb-0 small"><?php echo e(Str::limit($evaluation->notes, 100)); ?></p>
                                </div>
                            <?php endif; ?>
                            <?php if($evaluation->feedback): ?>
                                <div class="mb-2">
                                    <small class="text-muted d-block"><?php echo e(__('My Feedback')); ?></small>
                                    <p class="mb-0 small"><?php echo e(Str::limit($evaluation->feedback, 100)); ?></p>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <div class="mb-3">
                        <div class="row g-2">
                            <?php if($evaluation->assigned_at): ?>
                                <div class="col-6">
                                    <small class="text-muted d-block"><?php echo e(__('Assigned')); ?></small>
                                    <strong><?php echo e($evaluation->assigned_at->format('M d, Y')); ?></strong>
                                </div>
                            <?php endif; ?>
                            <?php if($evaluation->completed_at): ?>
                                <div class="col-6">
                                    <small class="text-muted d-block"><?php echo e(__('Completed')); ?></small>
                                    <strong><?php echo e($evaluation->completed_at->format('M d, Y')); ?></strong>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <?php echo e(__('Added by')); ?>: <?php echo e($evaluation->candidate->creator->name ?? 'Unknown'); ?>

                        </small>
                        <div>
                            <a href="<?php echo e(route('administration.hiring.show', $evaluation->candidate)); ?>" 
                               class="btn btn-sm btn-primary">
                                <i class="ti ti-eye"></i> <?php echo e(__('View Details')); ?>

                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ti ti-clipboard-off display-4 text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo e(__('No evaluations assigned')); ?></h5>
                    <p class="text-muted"><?php echo e(__('You have no candidate evaluations assigned to you at the moment.')); ?></p>
                    <a href="<?php echo e(route('administration.hiring.index')); ?>" class="btn btn-primary">
                        <i class="ti ti-users"></i> <?php echo e(__('View All Candidates')); ?>

                    </a>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if($evaluations->hasPages()): ?>
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                <?php echo e($evaluations->links()); ?>

            </div>
        </div>
    </div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            // Any custom JavaScript can go here
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/hiring/my_evaluations.blade.php ENDPATH**/ ?>