[2025-08-04 18:51:58] local.ERROR: Target class [Database\\Seeders\\Permission\\PermissionsTableSeeder] does not exist. {"exception":"[object] (Illuminate\\Contracts\\Container\\BindingResolutionException(code: 0): Target class [Database\\\\Seeders\\\\Permission\\\\PermissionsTableSeeder] does not exist. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:914)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('Database\\\\\\\\Seede...')
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Database\\\\\\\\Seede...', Array, true)
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Database\\\\\\\\Seede...', Array)
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Database\\\\\\\\Seede...', Array)
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(98): Illuminate\\Foundation\\Application->make('Database\\\\\\\\Seede...')
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Console\\Seeds\\SeedCommand->getSeeder()
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 {main}

[previous exception] [object] (ReflectionException(code: -1): Class \"Database\\\\Seeders\\\\Permission\\\\PermissionsTableSeeder\" does not exist at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php:912)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(912): ReflectionClass->__construct('Database\\\\\\\\Seede...')
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('Database\\\\\\\\Seede...')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('Database\\\\\\\\Seede...', Array, true)
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('Database\\\\\\\\Seede...', Array)
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('Database\\\\\\\\Seede...', Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(98): Illuminate\\Foundation\\Application->make('Database\\\\\\\\Seede...')
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(70): Illuminate\\Database\\Console\\Seeds\\SeedCommand->getSeeder()
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Concerns\\GuardsAttributes.php(155): Illuminate\\Database\\Console\\Seeds\\SeedCommand->Illuminate\\Database\\Console\\Seeds\\{closure}()
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Console\\Seeds\\SeedCommand.php(69): Illuminate\\Database\\Eloquent\\Model::unguarded(Object(Closure))
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Seeds\\SeedCommand->handle()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(180): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Application.php(1096): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Seeds\\SeedCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(201): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#22 {main}
"} 
[2025-08-04 19:15:21] local.ERROR: Route [administration.dashboard] not defined. {"view":{"view":"E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\create.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1150</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [administration.dashboard] not defined. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('administration....', Array, true)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\create.blade.php(39): route('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [administration.dashboard] not defined. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('administration....', Array, true)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\64f3fc24e4fc91ebd95518d767ecb323.php(37): route('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 {main}
"} 
[2025-08-04 13:17:41] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('encrypter', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(172): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(59): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#46 {main}
"} 
[2025-08-04 13:17:41] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(320): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('encrypter', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(986): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(971): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(255): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(55): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Response))
#19 {main}
"} 
[2025-08-04 19:17:43] local.ERROR: Route [administration.dashboard] not defined. {"view":{"view":"E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\show.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-508554976 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1151</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-508554976\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","hiring_candidate":"<pre class=sf-dump id=sf-dump-1682194468 data-indent-pad=\"  \"><span class=sf-dump-note>App\\Models\\Hiring\\HiringCandidate</span> {<a class=sf-dump-ref>#2190</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"17 characters\">hiring_candidates</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
  +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
  #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
  +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Nigel</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"15 characters\">****** 434-7576</span>\"
    \"<span class=sf-dump-key>expected_role</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ipsum Hic Et Consequ</span>\"
    \"<span class=sf-dump-key>expected_salary</span>\" => \"<span class=sf-dump-str title=\"8 characters\">25000.00</span>\"
    \"<span class=sf-dump-key>notes</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Fugiat eligendi inv</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">shortlisted</span>\"
    \"<span class=sf-dump-key>current_stage</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>hired_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 19:17:36</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 19:17:36</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:15</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>name</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Nigel</span>\"
    \"<span class=sf-dump-key>email</span>\" => \"<span class=sf-dump-str title=\"24 characters\"><EMAIL></span>\"
    \"<span class=sf-dump-key>phone</span>\" => \"<span class=sf-dump-str title=\"15 characters\">****** 434-7576</span>\"
    \"<span class=sf-dump-key>expected_role</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ipsum Hic Et Consequ</span>\"
    \"<span class=sf-dump-key>expected_salary</span>\" => \"<span class=sf-dump-str title=\"8 characters\">25000.00</span>\"
    \"<span class=sf-dump-key>notes</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Fugiat eligendi inv</span>\"
    \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"11 characters\">shortlisted</span>\"
    \"<span class=sf-dump-key>current_stage</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>created_by</span>\" => <span class=sf-dump-num>1</span>
    \"<span class=sf-dump-key>user_id</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>hired_at</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 19:17:36</span>\"
    \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-08-04 19:17:36</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>expected_salary</span>\" => \"<span class=sf-dump-str title=\"9 characters\">decimal:2</span>\"
    \"<span class=sf-dump-key>hired_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
    \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>creator</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#2574</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:13</span> [ &#8230;13]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:13</span> [ &#8230;13]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
      +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-const>null</span>
    \"<span class=sf-dump-key>evaluations</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2603</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
    \"<span class=sf-dump-key>files</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2675</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
  +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
  +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:11</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">name</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">email</span>\"
    <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"5 characters\">phone</span>\"
    <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"13 characters\">expected_role</span>\"
    <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"15 characters\">expected_salary</span>\"
    <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"5 characters\">notes</span>\"
    <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"
    <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"13 characters\">current_stage</span>\"
    <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"10 characters\">created_by</span>\"
    <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"7 characters\">user_id</span>\"
    <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"8 characters\">hired_at</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">evaluations</span>\"
    <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"5 characters\">files</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
  +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
  +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
  #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1682194468\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","stages":"<pre class=sf-dump id=sf-dump-751683350 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2744</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2743</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2742</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2741</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-751683350\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","evaluators":"<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2738</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#2764</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
      +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#2763</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
      +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\User
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">User</span></span> {<a class=sf-dump-ref>#2762</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"5 characters\">users</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:7</span> [ &#8230;7]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">rememberTokenName</span>: \"<span class=sf-dump-str title=\"14 characters\">remember_token</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dates</span>: <span class=sf-dump-note>array:3</span> [ &#8230;3]
      #<span class=sf-dump-protected title=\"Protected property\">accessToken</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">roleClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">permissionClass</span>: <span class=sf-dump-const>null</span>
      -<span class=sf-dump-private title=\"Private property defined in class:&#10;`App\\Models\\User`\">wildcardClass</span>: <span class=sf-dump-const>null</span>
      +<span class=sf-dump-public title=\"Public property\">mediaConversions</span>: []
      +<span class=sf-dump-public title=\"Public property\">mediaCollections</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">deletePreservingMedia</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">unAttachedMediaLibraryItems</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [administration.dashboard] not defined. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('administration....', Array, true)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\show.blade.php(81): route('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [administration.dashboard] not defined. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('administration....', Array, true)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\a5e6e524b7b3b1e9a9505865d96b9555.php(79): route('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 {main}
"} 
[2025-08-04 19:18:09] local.ERROR: View [administration.hiring.my_evaluations] not found. {"userId":1,"exception":"[object] (InvalidArgumentException(code: 0): View [administration.hiring.my_evaluations] not found. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:137)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(79): Illuminate\\View\\FileViewFinder->findInPaths('administration....', Array)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(137): Illuminate\\View\\FileViewFinder->find('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1020): Illuminate\\View\\Factory->make('administration....', Array, Array)
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php(169): view('administration....', Array)
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Administration\\Hiring\\HiringController->myEvaluations(Object(Illuminate\\Http\\Request))
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('myEvaluations', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Administration\\Hiring\\HiringController), 'myEvaluations')
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 {main}
"} 
[2025-08-04 19:21:51] local.ERROR: Route [administration.dashboard] not defined. {"view":{"view":"E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\index.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-1925713200 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1149</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-1925713200\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","candidates":"<pre class=sf-dump id=sf-dump-98549912 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\LengthAwarePaginator</span> {<a class=sf-dump-ref>#2650</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2591</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringCandidate
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringCandidate</span></span> {<a class=sf-dump-ref>#2525</a> &#8230;36}
    </samp>]
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"34 characters\">https://blueorange.test/hiring/all</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"34 characters\">https://blueorange.test/hiring/all</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-98549912\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","stages":"<pre class=sf-dump id=sf-dump-1972147463 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2683</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2682</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2681</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2680</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1972147463\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","statuses":"<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>shortlisted</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Shortlisted</span>\"
  \"<span class=sf-dump-key>in_progress</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"
  \"<span class=sf-dump-key>rejected</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Rejected</span>\"
  \"<span class=sf-dump-key>hired</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hired</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Route [administration.dashboard] not defined. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('administration....', Array, true)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\index.blade.php(61): route('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 {main}

[previous exception] [object] (Symfony\\Component\\Routing\\Exception\\RouteNotFoundException(code: 0): Route [administration.dashboard] not defined. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\UrlGenerator.php:477)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(811): Illuminate\\Routing\\UrlGenerator->route('administration....', Array, true)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\ad74f9b27a4ba85eabd8eb184a882f8b.php(59): route('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#77 {main}
"} 
[2025-08-04 19:22:31] local.ERROR: Call to undefined method stdClass::filled() {"view":{"view":"E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\index.blade.php","data":{"errors":"<pre class=sf-dump id=sf-dump-172000363 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Support\\ViewErrorBag</span> {<a class=sf-dump-ref>#1149</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []
</samp>}
</pre><script>Sfdump(\"sf-dump-172000363\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","candidates":"<pre class=sf-dump id=sf-dump-1254841285 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Pagination\\LengthAwarePaginator</span> {<a class=sf-dump-ref>#2650</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref>#2591</a><samp data-depth=2 class=sf-dump-compact>
    #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>
      <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringCandidate
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringCandidate</span></span> {<a class=sf-dump-ref>#2525</a> &#8230;36}
    </samp>]
    #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
  </samp>}
  #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
  #<span class=sf-dump-protected title=\"Protected property\">currentPage</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">path</span>: \"<span class=sf-dump-str title=\"34 characters\">https://blueorange.test/hiring/all</span>\"
  #<span class=sf-dump-protected title=\"Protected property\">query</span>: []
  #<span class=sf-dump-protected title=\"Protected property\">fragment</span>: <span class=sf-dump-const>null</span>
  #<span class=sf-dump-protected title=\"Protected property\">pageName</span>: \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  +<span class=sf-dump-public title=\"Public property\">onEachSide</span>: <span class=sf-dump-num>3</span>
  #<span class=sf-dump-protected title=\"Protected property\">options</span>: <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>
    \"<span class=sf-dump-key>path</span>\" => \"<span class=sf-dump-str title=\"34 characters\">https://blueorange.test/hiring/all</span>\"
    \"<span class=sf-dump-key>pageName</span>\" => \"<span class=sf-dump-str title=\"4 characters\">page</span>\"
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">total</span>: <span class=sf-dump-num>1</span>
  #<span class=sf-dump-protected title=\"Protected property\">lastPage</span>: <span class=sf-dump-num>1</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1254841285\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","stages":"<pre class=sf-dump id=sf-dump-1410147745 data-indent-pad=\"  \"><span class=sf-dump-note>Illuminate\\Database\\Eloquent\\Collection</span> {<a class=sf-dump-ref>#2683</a><samp data-depth=1 class=sf-dump-expanded>
  #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:3</span> [<samp data-depth=2 class=sf-dump-compact>
    <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2682</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2681</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
    <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Hiring\\HiringStage
\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Hiring</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">HiringStage</span></span> {<a class=sf-dump-ref>#2680</a><samp data-depth=3 class=sf-dump-compact>
      #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"13 characters\">hiring_stages</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"
      #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"
      +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>
      #<span class=sf-dump-protected title=\"Protected property\">with</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []
      +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>
      +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:8</span> [ &#8230;8]
      #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:2</span> [ &#8230;2]
      #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>
      #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []
      +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>
      +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>
      #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []
      #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:4</span> [ &#8230;4]
      #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [ &#8230;1]
      #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>
    </samp>}
  </samp>]
  #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>
</samp>}
</pre><script>Sfdump(\"sf-dump-1410147745\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
","statuses":"<pre class=sf-dump id=sf-dump-1702175426 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>
  \"<span class=sf-dump-key>shortlisted</span>\" => \"<span class=sf-dump-str title=\"11 characters\">Shortlisted</span>\"
  \"<span class=sf-dump-key>in_progress</span>\" => \"<span class=sf-dump-str title=\"11 characters\">In Progress</span>\"
  \"<span class=sf-dump-key>rejected</span>\" => \"<span class=sf-dump-str title=\"8 characters\">Rejected</span>\"
  \"<span class=sf-dump-key>hired</span>\" => \"<span class=sf-dump-str title=\"5 characters\">Hired</span>\"
</samp>]
</pre><script>Sfdump(\"sf-dump-1702175426\", {\"maxDepth\":3,\"maxStringLength\":160})</script>
"}},"userId":1,"exception":"[object] (Spatie\\LaravelIgnition\\Exceptions\\ViewException(code: 0): Call to undefined method stdClass::filled() at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Hiring\\HiringService.php:168)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Livewire\\Administration\\Hiring\\HiringCandidateFilter.php(79): App\\Services\\Administration\\Hiring\\HiringService->getCandidatesQuery(Object(stdClass))
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Livewire\\Administration\\Hiring\\HiringCandidateFilter->render()
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(271): Livewire\\Wrapped->__call('render', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(231): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->getView(Object(App\\Livewire\\Administration\\Hiring\\HiringCandidateFilter))
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Administration\\Hiring\\HiringCandidateFilter), '<div></div>')
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('administration....', Array, 'lw-241198431-0')
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\administration\\hiring\\index.blade.php(80): Livewire\\LivewireManager->mount('administration....', Array, 'lw-241198431-0', Array, Array)
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#84 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#85 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#86 {main}

[previous exception] [object] (Error(code: 0): Call to undefined method stdClass::filled() at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Services\\Administration\\Hiring\\HiringService.php:168)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Livewire\\Administration\\Hiring\\HiringCandidateFilter.php(79): App\\Services\\Administration\\Hiring\\HiringService->getCandidatesQuery(Object(stdClass))
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Livewire\\Administration\\Hiring\\HiringCandidateFilter->render()
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Wrapped.php(23): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(271): Livewire\\Wrapped->__call('render', Array)
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(231): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->getView(Object(App\\Livewire\\Administration\\Hiring\\HiringCandidateFilter))
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\HandleComponents\\HandleComponents.php(54): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->render(Object(App\\Livewire\\Administration\\Hiring\\HiringCandidateFilter), '<div></div>')
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\LivewireManager.php(73): Livewire\\Mechanisms\\HandleComponents\\HandleComponents->mount('administration....', Array, 'lw-241198431-0')
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\ad74f9b27a4ba85eabd8eb184a882f8b.php(85): Livewire\\LivewireManager->mount('administration....', Array, 'lw-241198431-0', Array, Array)
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(123): require('E:\\\\NIGEL\\\\Larave...')
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Filesystem\\Filesystem.php(124): Illuminate\\Filesystem\\Filesystem::Illuminate\\Filesystem\\{closure}()
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\PhpEngine.php(58): Illuminate\\Filesystem\\Filesystem->getRequire('E:\\\\NIGEL\\\\Larave...', Array)
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(22): Illuminate\\View\\Engines\\PhpEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Engines\\CompilerEngine.php(72): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->evaluatePath('E:\\\\NIGEL\\\\Larave...', Array)
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine.php(10): Illuminate\\View\\Engines\\CompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(207): Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine->get('E:\\\\NIGEL\\\\Larave...', Array)
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(190): Illuminate\\View\\View->getContents()
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\View.php(159): Illuminate\\View\\View->renderContents()
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(69): Illuminate\\View\\View->render()
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Response.php(35): Illuminate\\Http\\Response->setContent(Object(Illuminate\\View\\View))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(918): Illuminate\\Http\\Response->__construct(Object(Illuminate\\View\\View), 200, Array)
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(885): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\View\\View))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#73 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#74 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#75 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#76 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#77 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#78 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#79 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#80 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#81 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#82 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#83 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#84 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#85 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#86 {main}
"} 
[2025-08-04 19:24:11] local.ERROR: View [administration.hiring.my_evaluations] not found. {"userId":1,"exception":"[object] (InvalidArgumentException(code: 0): View [administration.hiring.my_evaluations] not found. at E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php:137)
[stacktrace]
#0 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\FileViewFinder.php(79): Illuminate\\View\\FileViewFinder->findInPaths('administration....', Array)
#1 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Factory.php(137): Illuminate\\View\\FileViewFinder->find('administration....')
#2 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\helpers.php(1020): Illuminate\\View\\Factory->make('administration....', Array, Array)
#3 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\Administration\\Hiring\\HiringController.php(169): view('administration....', Array)
#4 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Administration\\Hiring\\HiringController->myEvaluations(Object(Illuminate\\Http\\Request))
#5 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('myEvaluations', Array)
#6 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Administration\\Hiring\\HiringController), 'myEvaluations')
#7 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#8 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(806): Illuminate\\Routing\\Route->run()
#9 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#10 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authorize.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authorize->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'Employee Hiring...')
#12 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictIpRange.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictIpRange->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\RestrictDevices.php(46): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\RestrictDevices->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\UnrestrictedUser.php(25): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\UnrestrictedUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\Localization.php(20): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\Localization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Middleware\\CheckActiveUser.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): App\\Http\\Middleware\\CheckActiveUser->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\realrashid\\sweet-alert\\src\\ToSweetAlert.php(69): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): RealRashid\\SweetAlert\\ToSweetAlert->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#36 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(805): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(784): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(748): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(737): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(144): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\livewire\\livewire\\src\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware.php(19): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\barryvdh\\laravel-debugbar\\src\\Middleware\\InjectDebugbar.php(66): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Barryvdh\\Debugbar\\Middleware\\InjectDebugbar->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(99): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(183): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(119): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 {main}
"} 
