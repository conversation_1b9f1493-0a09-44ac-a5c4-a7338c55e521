<div>
    <div class="card border-primary">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0 text-white">
                <i class="ti ti-clipboard-check"></i> {{ __('Evaluate') }} {{ $stage->name }}
            </h6>
        </div>
        <div class="card-body">
            <form wire:submit.prevent="save">
                <div class="row g-3">
                    <!-- Assigned Evaluator -->
                    <div class="col-md-6">
                        <label for="assignedTo" class="form-label">{{ __('Assigned Evaluator') }} <span class="text-danger">*</span></label>
                        <select class="form-select @error('assignedTo') is-invalid @enderror" 
                                wire:model="assignedTo" 
                                id="assignedTo">
                            <option value="">{{ __('Select Evaluator') }}</option>
                            @foreach($evaluators as $evaluator)
                                <option value="{{ $evaluator->id }}">{{ $evaluator->name }}</option>
                            @endforeach
                        </select>
                        @error('assignedTo')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Status -->
                    <div class="col-md-6">
                        <label for="status" class="form-label">{{ __('Status') }} <span class="text-danger">*</span></label>
                        <select class="form-select @error('status') is-invalid @enderror" 
                                wire:model="status" 
                                id="status">
                            <option value="pending">{{ __('Pending') }}</option>
                            <option value="in_progress">{{ __('In Progress') }}</option>
                            <option value="completed">{{ __('Completed') }}</option>
                            <option value="passed">{{ __('Passed') }}</option>
                            <option value="failed">{{ __('Failed') }}</option>
                        </select>
                        @error('status')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Rating -->
                    <div class="col-md-6">
                        <label for="rating" class="form-label">{{ __('Rating (1-10)') }}</label>
                        <select class="form-select @error('rating') is-invalid @enderror" 
                                wire:model="rating" 
                                id="rating">
                            <option value="">{{ __('No Rating') }}</option>
                            @for($i = 1; $i <= 10; $i++)
                                <option value="{{ $i }}">{{ $i }} - {{ $i <= 3 ? 'Poor' : ($i <= 6 ? 'Average' : ($i <= 8 ? 'Good' : 'Excellent')) }}</option>
                            @endfor
                        </select>
                        @error('rating')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Notes -->
                    <div class="col-12">
                        <label for="notes" class="form-label">{{ __('Notes') }}</label>
                        <textarea class="form-control @error('notes') is-invalid @enderror" 
                                  wire:model="notes" 
                                  id="notes" 
                                  rows="3" 
                                  placeholder="{{ __('Add any notes about this evaluation stage...') }}"></textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Feedback -->
                    <div class="col-12">
                        <label for="feedback" class="form-label">{{ __('Feedback') }}</label>
                        <textarea class="form-control @error('feedback') is-invalid @enderror" 
                                  wire:model="feedback" 
                                  id="feedback" 
                                  rows="3" 
                                  placeholder="{{ __('Provide detailed feedback for the candidate...') }}"></textarea>
                        @error('feedback')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- File Upload -->
                    <div class="col-12">
                        <label for="files" class="form-label">{{ __('Evaluation Documents') }}</label>
                        <input type="file" 
                               class="form-control @error('files.*') is-invalid @enderror" 
                               wire:model="files" 
                               id="files" 
                               multiple 
                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        <div class="form-text">{{ __('Upload any relevant documents for this evaluation. Max 5MB per file.') }}</div>
                        @error('files.*')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        
                        <!-- File Preview -->
                        @if(!empty($files))
                            <div class="mt-2">
                                <small class="text-muted">{{ __('Selected files:') }}</small>
                                @foreach($files as $index => $file)
                                    <div class="d-flex align-items-center justify-content-between border rounded p-2 mt-1">
                                        <span class="small">{{ $file->getClientOriginalName() }}</span>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger" 
                                                wire:click="removeFile({{ $index }})">
                                            <i class="ti ti-x"></i>
                                        </button>
                                    </div>
                                @endforeach
                            </div>
                        @endif
                    </div>

                    <!-- Submit Button -->
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <button type="submit" 
                                    class="btn btn-primary" 
                                    wire:loading.attr="disabled">
                                <span wire:loading.remove>
                                    <i class="ti ti-device-floppy"></i> {{ __('Save Evaluation') }}
                                </span>
                                <span wire:loading>
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    {{ __('Saving...') }}
                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Existing Evaluation Info -->
            @if($evaluation)
                <div class="mt-4 pt-3 border-top">
                    <h6 class="text-muted mb-3">{{ __('Evaluation History') }}</h6>
                    <div class="row g-2">
                        @if($evaluation->assigned_at)
                            <div class="col-md-6">
                                <small class="text-muted d-block">{{ __('Assigned At') }}</small>
                                <strong>{{ $evaluation->assigned_at->format('M d, Y \a\t g:i A') }}</strong>
                            </div>
                        @endif
                        @if($evaluation->started_at)
                            <div class="col-md-6">
                                <small class="text-muted d-block">{{ __('Started At') }}</small>
                                <strong>{{ $evaluation->started_at->format('M d, Y \a\t g:i A') }}</strong>
                            </div>
                        @endif
                        @if($evaluation->completed_at)
                            <div class="col-md-6">
                                <small class="text-muted d-block">{{ __('Completed At') }}</small>
                                <strong>{{ $evaluation->completed_at->format('M d, Y \a\t g:i A') }}</strong>
                            </div>
                        @endif
                        @if($evaluation->duration)
                            <div class="col-md-6">
                                <small class="text-muted d-block">{{ __('Duration') }}</small>
                                <strong>{{ $evaluation->duration }}</strong>
                            </div>
                        @endif
                        @if($evaluation->creator)
                            <div class="col-md-6">
                                <small class="text-muted d-block">{{ __('Created By') }}</small>
                                <strong>{{ $evaluation->creator->name }}</strong>
                            </div>
                        @endif
                        @if($evaluation->updater)
                            <div class="col-md-6">
                                <small class="text-muted d-block">{{ __('Last Updated By') }}</small>
                                <strong>{{ $evaluation->updater->name }}</strong>
                            </div>
                        @endif
                    </div>
                </div>
            @endif
        </div>
    </div>

    <!-- Loading Overlay -->
    <div wire:loading.flex class="position-absolute top-0 start-0 w-100 h-100 bg-white bg-opacity-75 d-flex align-items-center justify-content-center" style="z-index: 1000;">
        <div class="text-center">
            <div class="spinner-border text-primary mb-2" role="status"></div>
            <div class="text-muted">{{ __('Processing...') }}</div>
        </div>
    </div>
</div>
