<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Candidate Details')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        .stage-timeline {
            position: relative;
        }
        .stage-timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: #e9ecef;
        }
        .stage-item {
            position: relative;
            padding-left: 60px;
            margin-bottom: 30px;
        }
        .stage-icon {
            position: absolute;
            left: 0;
            top: 0;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            z-index: 1;
        }
        .stage-completed {
            background-color: #28a745;
            color: white;
        }
        .stage-current {
            background-color: #ffc107;
            color: #212529;
        }
        .stage-pending {
            background-color: #e9ecef;
            color: #6c757d;
        }
        .evaluation-card {
            border-left: 4px solid #e9ecef;
        }
        .evaluation-card.status-passed {
            border-left-color: #28a745;
        }
        .evaluation-card.status-failed {
            border-left-color: #dc3545;
        }
        .evaluation-card.status-in-progress {
            border-left-color: #ffc107;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Candidate Details')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.hiring.index')); ?>"><?php echo e(__('Employee Hiring')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e($hiring_candidate->name); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Candidate Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <h4 class="mb-1"><?php echo e($hiring_candidate->name); ?></h4>
                        <p class="text-muted mb-2"><?php echo e($hiring_candidate->expected_role); ?></p>
                        <div class="d-flex gap-3 mb-3">
                            <small class="text-muted">
                                <i class="ti ti-mail"></i> <?php echo e($hiring_candidate->email); ?>

                            </small>
                            <small class="text-muted">
                                <i class="ti ti-phone"></i> <?php echo e($hiring_candidate->phone); ?>

                            </small>
                            <?php if($hiring_candidate->expected_salary): ?>
                                <small class="text-muted">
                                    <i class="ti ti-currency-rupee"></i> <?php echo e($hiring_candidate->expected_salary_formatted); ?>

                                </small>
                            <?php endif; ?>
                        </div>
                        <span class="badge <?php echo e($hiring_candidate->status_badge_class); ?> fs-6">
                            <?php echo e($hiring_candidate->status_formatted); ?>

                        </span>
                    </div>
                    <div class="text-end">
                        <div class="dropdown">
                            <button class="btn btn-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <?php echo e(__('Actions')); ?>

                            </button>
                            <ul class="dropdown-menu">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Update'])): ?>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('administration.hiring.edit', $hiring_candidate)); ?>">
                                            <i class="ti ti-edit"></i> <?php echo e(__('Edit Candidate')); ?>

                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if($hiring_candidate->status === 'in_progress' && $hiring_candidate->current_stage >= 3): ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything'])): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('administration.hiring.complete.form', $hiring_candidate)); ?>">
                                                <i class="ti ti-user-plus"></i> <?php echo e(__('Complete Hiring')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if($hiring_candidate->status !== 'rejected' && $hiring_candidate->status !== 'hired'): ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Update'])): ?>
                                        <li><hr class="dropdown-divider"></li>
                                        <li>
                                            <a class="dropdown-item text-warning" href="#" onclick="rejectCandidate()">
                                                <i class="ti ti-x"></i> <?php echo e(__('Reject Candidate')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Left Column - Candidate Info & Files -->
    <div class="col-lg-4">
        <!-- Candidate Information -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><?php echo e(__('Candidate Information')); ?></h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <small class="text-muted d-block"><?php echo e(__('Added by')); ?></small>
                    <strong><?php echo e($hiring_candidate->creator->name ?? 'Unknown'); ?></strong>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block"><?php echo e(__('Added on')); ?></small>
                    <strong><?php echo e($hiring_candidate->created_at->format('M d, Y \a\t g:i A')); ?></strong>
                </div>
                <div class="mb-3">
                    <small class="text-muted d-block"><?php echo e(__('Current Stage')); ?></small>
                    <strong><?php echo e($hiring_candidate->current_stage_name); ?></strong>
                </div>
                <?php if($hiring_candidate->hired_at): ?>
                    <div class="mb-3">
                        <small class="text-muted d-block"><?php echo e(__('Hired on')); ?></small>
                        <strong><?php echo e($hiring_candidate->hired_at->format('M d, Y \a\t g:i A')); ?></strong>
                    </div>
                <?php endif; ?>
                <?php if($hiring_candidate->notes): ?>
                    <div class="mb-3">
                        <small class="text-muted d-block"><?php echo e(__('Notes')); ?></small>
                        <p class="mb-0"><?php echo e($hiring_candidate->notes); ?></p>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Files -->
        <?php if($hiring_candidate->files->count() > 0): ?>
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><?php echo e(__('Documents')); ?></h6>
                </div>
                <div class="card-body">
                    <?php $__currentLoopData = $hiring_candidate->files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <div class="d-flex align-items-center mb-2">
                            <i class="ti ti-file-text me-2"></i>
                            <div class="flex-grow-1">
                                <a href="<?php echo e(Storage::url($file->file_path)); ?>" target="_blank" class="text-decoration-none">
                                    <?php echo e($file->file_name); ?>

                                </a>
                                <?php if($file->note): ?>
                                    <small class="text-muted d-block"><?php echo e($file->note); ?></small>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <!-- Right Column - Stage Timeline -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0"><?php echo e(__('Hiring Process Timeline')); ?></h6>
            </div>
            <div class="card-body">
                <div class="stage-timeline">
                    <?php $__currentLoopData = $stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <?php
                            $evaluation = $hiring_candidate->evaluations->where('hiring_stage_id', $stage->id)->first();
                            $stageClass = 'stage-pending';
                            if ($stage->stage_order < $hiring_candidate->current_stage) {
                                $stageClass = 'stage-completed';
                            } elseif ($stage->stage_order == $hiring_candidate->current_stage) {
                                $stageClass = 'stage-current';
                            }
                        ?>
                        
                        <div class="stage-item">
                            <div class="stage-icon <?php echo e($stageClass); ?>">
                                <?php echo e($stage->stage_order); ?>

                            </div>
                            <div>
                                <h6 class="mb-1"><?php echo e($stage->name); ?></h6>
                                <p class="text-muted mb-2"><?php echo e($stage->description); ?></p>
                                
                                <?php if($evaluation): ?>
                                    <div class="evaluation-card card mb-3 status-<?php echo e($evaluation->status); ?>">
                                        <div class="card-body p-3">
                                            <div class="d-flex justify-content-between align-items-start mb-2">
                                                <div>
                                                    <strong><?php echo e(__('Assigned to')); ?>: <?php echo e($evaluation->assignedUser->name ?? 'Unknown'); ?></strong>
                                                    <br>
                                                    <small class="text-muted"><?php echo e(__('Status')); ?>: 
                                                        <span class="badge <?php echo e($evaluation->status_badge_class); ?>">
                                                            <?php echo e($evaluation->status_formatted); ?>

                                                        </span>
                                                    </small>
                                                </div>
                                                <?php if($evaluation->rating): ?>
                                                    <div class="text-end">
                                                        <small class="text-muted d-block"><?php echo e(__('Rating')); ?></small>
                                                        <strong><?php echo e($evaluation->rating); ?>/10</strong>
                                                    </div>
                                                <?php endif; ?>
                                            </div>
                                            
                                            <?php if($evaluation->notes): ?>
                                                <div class="mb-2">
                                                    <small class="text-muted d-block"><?php echo e(__('Notes')); ?></small>
                                                    <p class="mb-0"><?php echo e($evaluation->notes); ?></p>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if($evaluation->feedback): ?>
                                                <div class="mb-2">
                                                    <small class="text-muted d-block"><?php echo e(__('Feedback')); ?></small>
                                                    <p class="mb-0"><?php echo e($evaluation->feedback); ?></p>
                                                </div>
                                            <?php endif; ?>
                                            
                                            <?php if($evaluation->completed_at): ?>
                                                <small class="text-muted">
                                                    <?php echo e(__('Completed on')); ?>: <?php echo e($evaluation->completed_at->format('M d, Y \a\t g:i A')); ?>

                                                </small>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                <?php else: ?>
                                    <?php if($stage->stage_order <= $hiring_candidate->current_stage): ?>
                                        <div class="alert alert-info">
                                            <small><?php echo e(__('No evaluation assigned yet')); ?></small>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                                
                                <!-- Evaluation Form for Current Stage -->
                                <?php if($stage->stage_order == $hiring_candidate->current_stage && $hiring_candidate->status !== 'hired' && $hiring_candidate->status !== 'rejected'): ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Update'])): ?>
                                        <div class="mt-3">
                                            <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('administration.hiring.stage-evaluation-form', [
                                                'candidateId' => $hiring_candidate->id,
                                                'stageId' => $stage->id
                                            ]);

$__html = app('livewire')->mount($__name, $__params, 'lw-166980628-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                                        </div>
                                    <?php endif; ?>
                                <?php endif; ?>
                            </div>
                        </div>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Reject Candidate Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <form action="<?php echo e(route('administration.hiring.reject', $hiring_candidate)); ?>" method="POST">
                <?php echo csrf_field(); ?>
                <div class="modal-header">
                    <h5 class="modal-title"><?php echo e(__('Reject Candidate')); ?></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="reason" class="form-label"><?php echo e(__('Reason for Rejection')); ?></label>
                        <textarea class="form-control" id="reason" name="reason" rows="3" 
                                  placeholder="<?php echo e(__('Please provide a reason for rejecting this candidate...')); ?>"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal"><?php echo e(__('Cancel')); ?></button>
                    <button type="submit" class="btn btn-danger"><?php echo e(__('Reject Candidate')); ?></button>
                </div>
            </form>
        </div>
    </div>
</div>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        function rejectCandidate() {
            $('#rejectModal').modal('show');
        }

        // Listen for Livewire events
        document.addEventListener('livewire:init', () => {
            Livewire.on('evaluation-saved', (event) => {
                if (event.type === 'success') {
                    toastr.success(event.message);
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                }
            });

            Livewire.on('evaluation-error', (event) => {
                if (event.type === 'error') {
                    toastr.error(event.message);
                }
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/hiring/show.blade.php ENDPATH**/ ?>