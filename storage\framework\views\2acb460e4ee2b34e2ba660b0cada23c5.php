<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Roles')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    .more-user-avatar {
        background-color: #dddddd;
        border-radius: 50px;
        text-align: center;
        padding-top: 5px;
        border: 1px solid #ffffff;
    }
    .more-user-avatar small {
        font-size: 12px;
        color: #333333;
        font-weight: bold;
    }
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Roles')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Role & Permission')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('Role')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All Roles')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row g-4">
    <?php $__currentLoopData = $roles; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $role): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="col-xl-4 col-lg-6 col-md-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <h6 class="fw-normal mb-2">Total <strong><?php echo e($role->active_users_count); ?></strong> Active Users</h6>
                        <ul class="list-unstyled d-flex align-items-center avatar-group mb-0">
                            <?php $__currentLoopData = $role->users->take(5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="<?php echo e($user->alias_name); ?>" class="avatar avatar-sm pull-up">
                                    <?php if($user->hasMedia('avatar')): ?>
                                        <img src="<?php echo e($user->getFirstMediaUrl('avatar', 'thumb')); ?>" alt="Avatar" class="rounded-circle">
                                    <?php else: ?>
                                        <span class="avatar-initial rounded-circle bg-label-hover-dark text-bold">
                                            <?php echo e(profile_name_pic($user)); ?>

                                        </span>
                                    <?php endif; ?>
                                </li>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            <?php if($role->active_users_count > 5): ?>
                                <li data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="<?php echo e($role->active_users_count - 5); ?> More" class="avatar avatar-sm pull-up more-user-avatar">
                                    <small><?php echo e(($role->active_users_count - 5) < 10 ? $role->active_users_count - 5 : '9'); ?>+</small>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </div>
                    <div class="d-flex justify-content-between align-items-end mt-1">
                        <div class="role-heading">
                            <h4 class="mb-1"><?php echo e($role->name); ?></h4>
                            <span class="role-edit-modal">
                                <span>Total Permissions: <strong><?php echo e($role->permissions->count()); ?></strong></span>
                            </span>
                        </div>
                        <div>
                            <?php if(auth()->user()->hasRole('Developer') || ($role->name !== 'Developer' && $role->name !== 'Super Admin')): ?>
                                <a href="<?php echo e(route('administration.settings.rolepermission.role.edit', ['role' => $role])); ?>" class="text-muted" data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="Edit Role">
                                    <i class="ti ti-edit ti-md text-info"></i>
                                </a>
                            <?php endif; ?>

                            <a href="<?php echo e(route('administration.settings.rolepermission.role.show', ['role' => $role])); ?>" class="text-muted" data-bs-toggle="tooltip" data-popup="tooltip-custom" data-bs-placement="top" title="Show Role Details">
                                <i class="ti ti-info-hexagon ti-md text-primary"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <div class="col-xl-4 col-lg-6 col-md-6">
        <div class="card h-100">
            <div class="row h-100">
                <div class="col-sm-5">
                    <div class="d-flex align-items-end h-100 justify-content-center mt-sm-0 mt-3">
                        <img src="<?php echo e(asset('assets/img/illustrations/add-new-roles.png')); ?>" class="img-fluid mt-sm-4 mt-md-0" alt="add-new-roles" width="83" />
                    </div>
                </div>
                <div class="col-sm-7">
                    <div class="card-body text-sm-end text-center ps-sm-0">
                        <a href="<?php echo e(route('administration.settings.rolepermission.role.create')); ?>" class="btn btn-primary mb-2 text-nowrap add-new-role">
                            Add New Role
                        </a>
                        <p class="mb-0 mt-1">Add role, if it does not exist</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/role/index.blade.php ENDPATH**/ ?>