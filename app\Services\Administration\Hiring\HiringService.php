<?php

namespace App\Services\Administration\Hiring;

use Exception;
use App\Models\User;
use Illuminate\Support\Str;
use App\Models\Hiring\HiringStage;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use App\Models\Hiring\HiringCandidate;
use App\Models\User\Employee\Employee;
use App\Models\Hiring\HiringStageEvaluation;

class HiringService
{
    /**
     * Store a new hiring candidate
     */
    public function storeCandidate(array $data): HiringCandidate
    {
        return HiringCandidate::create([
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'expected_role' => $data['expected_role'],
            'expected_salary' => $data['expected_salary'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => 'shortlisted',
            'current_stage' => 1,
            'created_by' => auth()->id(),
        ]);
    }

    /**
     * Update a hiring candidate
     */
    public function updateCandidate(HiringCandidate $candidate, array $data): HiringCandidate
    {
        $candidate->update([
            'name' => $data['name'],
            'email' => $data['email'],
            'phone' => $data['phone'],
            'expected_role' => $data['expected_role'],
            'expected_salary' => $data['expected_salary'] ?? null,
            'notes' => $data['notes'] ?? null,
            'status' => $data['status'],
        ]);

        return $candidate->fresh();
    }

    /**
     * Create or update a stage evaluation
     */
    public function storeOrUpdateEvaluation(array $data): HiringStageEvaluation
    {
        $evaluation = HiringStageEvaluation::updateOrCreate(
            [
                'hiring_candidate_id' => $data['hiring_candidate_id'],
                'hiring_stage_id' => $data['hiring_stage_id'],
            ],
            [
                'assigned_to' => $data['assigned_to'],
                'status' => $data['status'],
                'notes' => $data['notes'] ?? null,
                'feedback' => $data['feedback'] ?? null,
                'rating' => $data['rating'] ?? null,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]
        );

        // Update timestamps based on status
        $this->updateEvaluationTimestamps($evaluation, $data['status']);

        return $evaluation;
    }

    /**
     * Progress candidate to next stage
     */
    public function progressToNextStage(HiringCandidate $candidate): bool
    {
        if ($candidate->current_stage >= 3) {
            return false; // Already at final stage
        }

        $candidate->update([
            'current_stage' => $candidate->current_stage + 1,
            'status' => 'in_progress'
        ]);

        return true;
    }

    /**
     * Reject a candidate
     */
    public function rejectCandidate(HiringCandidate $candidate, string $reason = null): HiringCandidate
    {
        $candidate->update([
            'status' => 'rejected',
            'notes' => $candidate->notes . ($reason ? "\n\nRejection Reason: " . $reason : '')
        ]);

        return $candidate;
    }

    /**
     * Complete hiring process and create user account
     */
    public function completeHiring(HiringCandidate $candidate, array $userData): User
    {
        return DB::transaction(function () use ($candidate, $userData) {
            // Create user account
            $user = User::create([
                'userid' => $userData['userid'],
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'name' => $userData['first_name'] . ' ' . $userData['last_name'],
                'email' => $userData['email'],
                'password' => Hash::make($userData['password']),
                'status' => 'Active',
            ]);

            // Create employee profile
            Employee::create([
                'user_id' => $user->id,
                'joining_date' => $userData['joining_date'],
                'alias_name' => $userData['alias_name'] ?? null,
                'official_email' => $userData['official_email'] ?? $userData['email'],
                'official_contact_no' => $userData['official_contact_no'] ?? $candidate->phone,
            ]);

            // Assign role
            if (isset($userData['role_id'])) {
                $role = \Spatie\Permission\Models\Role::find($userData['role_id']);
                if ($role) {
                    $user->assignRole($role);
                }
            }

            // Update candidate record
            $candidate->update([
                'status' => 'hired',
                'user_id' => $user->id,
                'hired_at' => now(),
            ]);

            return $user;
        });
    }

    /**
     * Get candidates with filters
     */
    public function getCandidatesQuery($request)
    {
        $query = HiringCandidate::with([
            'creator.employee',
            'user.employee',
            'evaluations.stage',
            'evaluations.assignedUser.employee'
        ]);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('stage')) {
            $query->where('current_stage', $request->stage);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('expected_role', 'like', "%{$search}%");
            });
        }

        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        return $query->orderByDesc('created_at');
    }

    /**
     * Get evaluations for a user
     */
    public function getMyEvaluationsQuery($userId)
    {
        return HiringStageEvaluation::with([
            'candidate',
            'stage',
            'creator.employee',
            'updater.employee'
        ])->where('assigned_to', $userId)
          ->orderByDesc('created_at');
    }

    /**
     * Update evaluation timestamps based on status
     */
    private function updateEvaluationTimestamps(HiringStageEvaluation $evaluation, string $status): void
    {
        $now = now();

        switch ($status) {
            case 'in_progress':
                if (!$evaluation->started_at) {
                    $evaluation->update(['started_at' => $now]);
                }
                break;
            case 'completed':
            case 'passed':
            case 'failed':
                if (!$evaluation->started_at) {
                    $evaluation->update(['started_at' => $now]);
                }
                if (!$evaluation->completed_at) {
                    $evaluation->update(['completed_at' => $now]);
                }
                break;
        }
    }

    /**
     * Get available evaluators (users with appropriate permissions)
     */
    public function getAvailableEvaluators()
    {
        return User::with(['employee', 'roles'])
            ->whereStatus('Active')
            ->whereHas('roles', function ($query) {
                $query->whereIn('name', ['Super Admin', 'Developer'])
                      ->orWhereHas('permissions', function ($permQuery) {
                          $permQuery->where('name', 'like', 'Employee Hiring%');
                      });
            })
            ->select(['id', 'name'])
            ->get();
    }
}
