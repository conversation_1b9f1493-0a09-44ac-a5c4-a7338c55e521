<div>
    <form wire:submit.prevent="$refresh">
        <div class="row g-3">
            <!-- Search -->
            <div class="col-md-3">
                <label for="search" class="form-label"><?php echo e(__('Search')); ?></label>
                <input type="text" 
                       class="form-control" 
                       id="search"
                       wire:model.live.debounce.300ms="search" 
                       placeholder="<?php echo e(__('Name, email, or role...')); ?>">
            </div>

            <!-- Status Filter -->
            <div class="col-md-2">
                <label for="status" class="form-label"><?php echo e(__('Status')); ?></label>
                <select class="form-select" id="status" wire:model.live="status">
                    <option value=""><?php echo e(__('All Statuses')); ?></option>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $statuses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($key); ?>"><?php echo e($label); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </select>
            </div>

            <!-- Stage Filter -->
            <div class="col-md-2">
                <label for="stage" class="form-label"><?php echo e(__('Current Stage')); ?></label>
                <select class="form-select" id="stage" wire:model.live="stage">
                    <option value=""><?php echo e(__('All Stages')); ?></option>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $stages; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $stage): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($stage->stage_order); ?>"><?php echo e($stage->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </select>
            </div>

            <!-- Date From -->
            <div class="col-md-2">
                <label for="dateFrom" class="form-label"><?php echo e(__('From Date')); ?></label>
                <input type="date" 
                       class="form-control" 
                       id="dateFrom"
                       wire:model.live="dateFrom">
            </div>

            <!-- Date To -->
            <div class="col-md-2">
                <label for="dateTo" class="form-label"><?php echo e(__('To Date')); ?></label>
                <input type="date" 
                       class="form-control" 
                       id="dateTo"
                       wire:model.live="dateTo">
            </div>

            <!-- Actions -->
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="button" 
                            class="btn btn-outline-secondary btn-sm" 
                            wire:click="clearFilters"
                            title="<?php echo e(__('Clear Filters')); ?>">
                        <i class="ti ti-x"></i>
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Results Summary -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        <?php echo e(__('Showing')); ?> <?php echo e($candidates->firstItem() ?? 0); ?> <?php echo e(__('to')); ?> <?php echo e($candidates->lastItem() ?? 0); ?> 
                        <?php echo e(__('of')); ?> <?php echo e($candidates->total()); ?> <?php echo e(__('candidates')); ?>

                    </small>
                </div>
                <div>
                    <select class="form-select form-select-sm" wire:model.live="perPage" style="width: auto;">
                        <option value="15">15 <?php echo e(__('per page')); ?></option>
                        <option value="25">25 <?php echo e(__('per page')); ?></option>
                        <option value="50">50 <?php echo e(__('per page')); ?></option>
                        <option value="100">100 <?php echo e(__('per page')); ?></option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div wire:loading class="text-center mt-3">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
            <span class="visually-hidden"><?php echo e(__('Loading...')); ?></span>
        </div>
        <small class="text-muted ms-2"><?php echo e(__('Filtering candidates...')); ?></small>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/livewire/administration/hiring/hiring-candidate-filter.blade.php ENDPATH**/ ?>