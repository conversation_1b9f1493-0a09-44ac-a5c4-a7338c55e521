<div>
    <form wire:submit.prevent="$refresh">
        <div class="row g-3">
            <!-- Search -->
            <div class="col-md-3">
                <label for="search" class="form-label">{{ __('Search') }}</label>
                <input type="text" 
                       class="form-control" 
                       id="search"
                       wire:model.live.debounce.300ms="search" 
                       placeholder="{{ __('Name, email, or role...') }}">
            </div>

            <!-- Status Filter -->
            <div class="col-md-2">
                <label for="status" class="form-label">{{ __('Status') }}</label>
                <select class="form-select" id="status" wire:model.live="status">
                    <option value="">{{ __('All Statuses') }}</option>
                    @foreach($statuses as $key => $label)
                        <option value="{{ $key }}">{{ $label }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Stage Filter -->
            <div class="col-md-2">
                <label for="stage" class="form-label">{{ __('Current Stage') }}</label>
                <select class="form-select" id="stage" wire:model.live="stage">
                    <option value="">{{ __('All Stages') }}</option>
                    @foreach($stages as $stage)
                        <option value="{{ $stage->stage_order }}">{{ $stage->name }}</option>
                    @endforeach
                </select>
            </div>

            <!-- Date From -->
            <div class="col-md-2">
                <label for="dateFrom" class="form-label">{{ __('From Date') }}</label>
                <input type="date" 
                       class="form-control" 
                       id="dateFrom"
                       wire:model.live="dateFrom">
            </div>

            <!-- Date To -->
            <div class="col-md-2">
                <label for="dateTo" class="form-label">{{ __('To Date') }}</label>
                <input type="date" 
                       class="form-control" 
                       id="dateTo"
                       wire:model.live="dateTo">
            </div>

            <!-- Actions -->
            <div class="col-md-1">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="button" 
                            class="btn btn-outline-secondary btn-sm" 
                            wire:click="clearFilters"
                            title="{{ __('Clear Filters') }}">
                        <i class="ti ti-x"></i>
                    </button>
                </div>
            </div>
        </div>
    </form>

    <!-- Results Summary -->
    <div class="row mt-3">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <small class="text-muted">
                        {{ __('Showing') }} {{ $candidates->firstItem() ?? 0 }} {{ __('to') }} {{ $candidates->lastItem() ?? 0 }} 
                        {{ __('of') }} {{ $candidates->total() }} {{ __('candidates') }}
                    </small>
                </div>
                <div>
                    <select class="form-select form-select-sm" wire:model.live="perPage" style="width: auto;">
                        <option value="15">15 {{ __('per page') }}</option>
                        <option value="25">25 {{ __('per page') }}</option>
                        <option value="50">50 {{ __('per page') }}</option>
                        <option value="100">100 {{ __('per page') }}</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading Indicator -->
    <div wire:loading class="text-center mt-3">
        <div class="spinner-border spinner-border-sm text-primary" role="status">
            <span class="visually-hidden">{{ __('Loading...') }}</span>
        </div>
        <small class="text-muted ms-2">{{ __('Filtering candidates...') }}</small>
    </div>
</div>
