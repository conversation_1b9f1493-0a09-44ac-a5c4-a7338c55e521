<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Update Role')); ?>

<?php $__env->startSection('css_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Update Role')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Role & Permission')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('Role')); ?></li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.settings.rolepermission.role.index')); ?>"><?php echo e(__('All Roles')); ?></a>
    </li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.settings.rolepermission.role.show', ['role' => $role])); ?>"><?php echo e($role->name); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Update Role')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">Update Role <b class="text-primary">(<?php echo e($role->name); ?>)</b></h5>
        
                <div class="card-header-elements ms-auto">
                    <a href="<?php echo e(route('administration.settings.rolepermission.role.show', ['role' => $role])); ?>" class="btn btn-sm btn-primary">
                        <span class="tf-icon ti ti-arrow-left ti-xs me-1"></span>
                        Back
                    </a>
                </div>
            </div>
            <div class="card-body">
                <form action="<?php echo e(route('administration.settings.rolepermission.role.update', ['role' => $role])); ?>" method="post" autocomplete="off">
                    <?php echo csrf_field(); ?>
                    <?php if($role->name !== 'Developer' && $role->name !== 'Super Admin'): ?> 
                        <div class="col-12 mb-4">
                            <label class="form-label" for="name">Role Name <strong class="text-danger">*</strong></label>
                            <input type="text" name="name" value="<?php echo e($role->name); ?>" class="form-control" placeholder="Enter a role name" tabindex="-1" required />
                        </div>
                    <?php endif; ?>
                    <div class="col-12">
                        <h5>Role Permissions</h5>
                        <!-- Permission table -->
                        <div class="table-responsive">
                            <table class="table table-flush-spacing">
                                <thead>
                                    <tr>
                                        <td class="text-nowrap fw-medium">
                                            <?php echo e($role->name); ?> Access 
                                            <i class="ti ti-info-circle" data-bs-toggle="tooltip" data-bs-placement="top" title="Allows a full access to the system"></i>
                                        </td>
                                        <td class="" colspan="4">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAllPermissions" />
                                                <label class="form-check-label" for="selectAllPermissions">
                                                    Select All Permissions
                                                </label>
                                            </div>
                                        </td>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                        <tr>
                                            <td class="text-nowrap fw-medium"><?php echo e($module->name); ?></td>
                                            <td>
                                                <!-- "Everything" Checkbox for the module -->
                                                <div class="d-flex">
                                                    <div class="form-check me-3 me-lg-5">
                                                        <input class="form-check-input everything-checkbox" type="checkbox" id="select_everything<?php echo e($module->id); ?>" data-module-id="<?php echo e($module->id); ?>" />
                                                        <label class="form-check-label" for="select_everything<?php echo e($module->id); ?>">
                                                            Select Everything
                                                        </label>
                                                    </div>
                                                </div>
                                            </td>
                                            <?php $__currentLoopData = $module->permissions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $sl => $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                                <td>
                                                    <div class="d-flex">
                                                        <div class="form-check me-3 me-lg-5">
                                                            <input class="form-check-input" type="checkbox" name="permissions[]" id="permission<?php echo e($permission->id); ?>" value="<?php echo e($permission->id); ?>" data-module-id="<?php echo e($module->id); ?>" <?php if($role->hasPermissionTo($permission)): echo 'checked'; endif; ?>/>
                                                            <label class="form-check-label" for="permission<?php echo e($permission->id); ?>">
                                                                <?php echo e($permission->name); ?>

                                                            </label>
                                                        </div>
                                                    </div>
                                                </td>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                        </div>
                        <!-- Permission table -->
                    </div>
                    <div class="col-12 mt-4">
                        <button type="submit" class="btn btn-primary float-end">Update Role</button>
                    </div>
                </form>
                <!--/ Add role form -->
            </div>            
        </div>        
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function () {
            // "Select All Permissions" checkbox behavior
            $("#selectAllPermissions").click(function () {
                var selectAllChecked = $(this).prop("checked");
                $("input[name='permissions[]']").prop("checked", selectAllChecked);
                $(".everything-checkbox").prop("checked", selectAllChecked);
            });
    
            // Individual permission checkbox click behavior
            $("input[name='permissions[]']").click(function () {
                var row = $(this).closest("tr");
                var moduleId = $(this).data("module-id");
    
                var everythingCheckbox = row.find(".everything-checkbox[data-module-id='" + moduleId + "']");
                var permissionsCheckboxes = row.find("input[name='permissions[]'][data-module-id='" + moduleId + "']");
    
                // Handle the "Everything" checkbox based on individual permissions
                if ($(this).prop("checked")) {
                    var allChecked = permissionsCheckboxes.filter(":checked").length === permissionsCheckboxes.length;
                    if (allChecked) {
                        everythingCheckbox.prop("checked", true);
                    }
                } else {
                    everythingCheckbox.prop("checked", false);
                }
    
                // Check the "Select All Permissions" checkbox if all individual permissions are selected
                var anyUnchecked = $("input[name='permissions[]']:not(:checked)").length > 0;
                $("#selectAllPermissions").prop("checked", !anyUnchecked);
            });
    
            // "Everything" checkbox click behavior
            $(".everything-checkbox").click(function () {
                var moduleId = $(this).data("module-id");
                var row = $(this).closest("tr");
                var permissionsCheckboxes = row.find("input[name='permissions[]'][data-module-id='" + moduleId + "']");
    
                // If "Everything" is checked, select all individual permissions for that module
                if ($(this).prop("checked")) {
                    permissionsCheckboxes.prop("checked", true);
                } else {
                    permissionsCheckboxes.prop("checked", false);
                }
    
                // Check the "Select All Permissions" checkbox if all permissions are selected
                var anyUnchecked = $("input[name='permissions[]']:not(:checked)").length > 0;
                $("#selectAllPermissions").prop("checked", !anyUnchecked);
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/role/edit.blade.php ENDPATH**/ ?>