<?php $__env->startSection('meta_tags'); ?>
    

<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Create New Permission')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
    /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Create New Permission')); ?></b>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Role & Permission')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('Permission')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('Create New Permission')); ?></li>
<?php $__env->stopSection(); ?>


<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">Create New Permission</h5>
        
                <div class="card-header-elements ms-auto">
                    <a href="<?php echo e(route('administration.settings.rolepermission.permission.index')); ?>" class="btn btn-sm btn-primary">
                        <span class="tf-icon ti ti-plus ti-xs me-1"></span>
                        All Permissions
                    </a>
                </div>
            </div>
            <form action="<?php echo e(route('administration.settings.rolepermission.permission.store')); ?>" method="post" autocomplete="off" name="sumbit_form" id="submitForm">
                <?php echo csrf_field(); ?>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-12">
                            <label class="form-label">Select Module <strong class="text-danger">*</strong></label>
                            <select class="select2 form-select" name="permission_module_id" data-allow-clear="true" required>
                                <option value="" selected disabled>Select Module</option>
                                <?php $__currentLoopData = $modules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $module): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?> 
                                    <option value="<?php echo e($module->id); ?>"><?php echo e($module->name); ?></option>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </select>
                            <small class="float-end pt-2">
                                Didn't Find Module? 
                                <a href="javascript:;" data-bs-toggle="modal" data-bs-target="#addNewPermissionModuleModal" class="text-primary text-bold">Create Module</a>
                            </small>
                        </div>                        
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-12">
                            <div class="d-flex">
                                <div class="form-check me-3 me-lg-5">
                                    <input class="form-check-input" type="checkbox" checked name="name[Everything]" id="permissionEverything" />
                                    <label class="form-check-label" for="permissionEverything">
                                        Everything
                                    </label>
                                </div>
                                <div class="form-check me-3 me-lg-5">
                                    <input class="form-check-input" type="checkbox" checked name="name[Create]" id="permissionCreate" />
                                    <label class="form-check-label" for="permissionCreate">
                                        Create
                                    </label>
                                </div>
                                <div class="form-check me-3 me-lg-5">
                                    <input class="form-check-input" type="checkbox" checked name="name[Read]" id="permissionRead" />
                                    <label class="form-check-label" for="permissionRead">
                                        Read
                                    </label>
                                </div>
                                <div class="form-check me-3 me-lg-5">
                                    <input class="form-check-input" type="checkbox" checked name="name[Update]" id="permissionUpdate" />
                                    <label class="form-check-label" for="permissionUpdate">
                                        Update
                                    </label>
                                </div>
                                <div class="form-check me-3 me-lg-5">
                                    <input class="form-check-input" type="checkbox" checked name="name[Delete]" id="permissionDelete" />
                                    <label class="form-check-label" for="permissionDelete">
                                        Delete
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-12 mt-4">
                            <button type="submit" class="btn btn-primary float-end">Create Permissions</button>
                        </div>
                    </div>
                </div>
            </form>
        </div>        
    </div>
</div>
<!-- End row -->


<!-- Add New Module Modal -->
<div class="modal fade" data-bs-backdrop="static" id="addNewPermissionModuleModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-centered">
        <div class="modal-content p-3 p-md-5">
            <button type="button" class="btn-close btn-pinned" data-bs-dismiss="modal" aria-label="Close"></button>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <h3 class="role-title mb-2">Add New Permission</h3>
                    <p class="text-muted">Create A New Permission Module</p>
                </div>
                <!-- Add New Module form -->
                <form method="post" action="<?php echo e(route('administration.settings.rolepermission.permission.module.store')); ?>" class="row g-3" autocomplete="off">
                    <?php echo csrf_field(); ?>
                    <div class="col-12 mb-4">
                        <label class="form-label">Module Name <strong class="text-danger">*</strong></label>
                        <input type="text" name="name" value="<?php echo e(old('name')); ?>" class="form-control" placeholder="Enter a Name" tabindex="-1" required/>
                        <?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <span class="text-danger"><?php echo e($message); ?></span>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                    </div>
                    <div class="col-12 text-center mt-4">
                        <button type="reset" class="btn btn-label-secondary" data-bs-dismiss="modal" aria-label="Close">Cancel</button>
                        <button type="submit" class="btn btn-primary me-sm-3 me-1">Create Module</button>
                    </div>
                </form>
                <!--/ Add New Module form -->
            </div>
        </div>
    </div>
</div>
<!--/ Add New Module Modal -->

<?php $__env->stopSection(); ?>


<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        // Custom Script Here
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/settings/permission/create.blade.php ENDPATH**/ ?>