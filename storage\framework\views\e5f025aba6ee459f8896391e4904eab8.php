<div>
    <div class="card border-primary">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0 text-white">
                <i class="ti ti-clipboard-check"></i> <?php echo e(__('Evaluate')); ?> <?php echo e($stage->name); ?>

            </h6>
        </div>
        <div class="card-body">
            <form wire:submit.prevent="save">
                <div class="row g-3">
                    <!-- Assigned Evaluator -->
                    <div class="col-md-6">
                        <label for="assignedTo" class="form-label"><?php echo e(__('Assigned Evaluator')); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php $__errorArgs = ['assignedTo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                wire:model="assignedTo" 
                                id="assignedTo">
                            <option value=""><?php echo e(__('Select Evaluator')); ?></option>
                            <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $evaluators; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $evaluator): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <option value="<?php echo e($evaluator->id); ?>"><?php echo e($evaluator->name); ?></option>
                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['assignedTo'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Status -->
                    <div class="col-md-6">
                        <label for="status" class="form-label"><?php echo e(__('Status')); ?> <span class="text-danger">*</span></label>
                        <select class="form-select <?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                wire:model="status" 
                                id="status">
                            <option value="pending"><?php echo e(__('Pending')); ?></option>
                            <option value="in_progress"><?php echo e(__('In Progress')); ?></option>
                            <option value="completed"><?php echo e(__('Completed')); ?></option>
                            <option value="passed"><?php echo e(__('Passed')); ?></option>
                            <option value="failed"><?php echo e(__('Failed')); ?></option>
                        </select>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['status'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Rating -->
                    <div class="col-md-6">
                        <label for="rating" class="form-label"><?php echo e(__('Rating (1-10)')); ?></label>
                        <select class="form-select <?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                wire:model="rating" 
                                id="rating">
                            <option value=""><?php echo e(__('No Rating')); ?></option>
                            <!--[if BLOCK]><![endif]--><?php for($i = 1; $i <= 10; $i++): ?>
                                <option value="<?php echo e($i); ?>"><?php echo e($i); ?> - <?php echo e($i <= 3 ? 'Poor' : ($i <= 6 ? 'Average' : ($i <= 8 ? 'Good' : 'Excellent'))); ?></option>
                            <?php endfor; ?><!--[if ENDBLOCK]><![endif]-->
                        </select>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['rating'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Notes -->
                    <div class="col-12">
                        <label for="notes" class="form-label"><?php echo e(__('Notes')); ?></label>
                        <textarea class="form-control <?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  wire:model="notes" 
                                  id="notes" 
                                  rows="3" 
                                  placeholder="<?php echo e(__('Add any notes about this evaluation stage...')); ?>"></textarea>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['notes'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Feedback -->
                    <div class="col-12">
                        <label for="feedback" class="form-label"><?php echo e(__('Feedback')); ?></label>
                        <textarea class="form-control <?php $__errorArgs = ['feedback'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                                  wire:model="feedback" 
                                  id="feedback" 
                                  rows="3" 
                                  placeholder="<?php echo e(__('Provide detailed feedback for the candidate...')); ?>"></textarea>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['feedback'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- File Upload -->
                    <div class="col-12">
                        <label for="files" class="form-label"><?php echo e(__('Evaluation Documents')); ?></label>
                        <input type="file" 
                               class="form-control <?php $__errorArgs = ['files.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" 
                               wire:model="files" 
                               id="files" 
                               multiple 
                               accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                        <div class="form-text"><?php echo e(__('Upload any relevant documents for this evaluation. Max 5MB per file.')); ?></div>
                        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['files.*'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                            <div class="invalid-feedback"><?php echo e($message); ?></div>
                        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
                        
                        <!-- File Preview -->
                        <!--[if BLOCK]><![endif]--><?php if(!empty($files)): ?>
                            <div class="mt-2">
                                <small class="text-muted"><?php echo e(__('Selected files:')); ?></small>
                                <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $files; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $file): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <div class="d-flex align-items-center justify-content-between border rounded p-2 mt-1">
                                        <span class="small"><?php echo e($file->getClientOriginalName()); ?></span>
                                        <button type="button" 
                                                class="btn btn-sm btn-outline-danger" 
                                                wire:click="removeFile(<?php echo e($index); ?>)">
                                            <i class="ti ti-x"></i>
                                        </button>
                                    </div>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>

                    <!-- Submit Button -->
                    <div class="col-12">
                        <div class="d-flex justify-content-end gap-2">
                            <button type="submit" 
                                    class="btn btn-primary" 
                                    wire:loading.attr="disabled">
                                <span wire:loading.remove>
                                    <i class="ti ti-device-floppy"></i> <?php echo e(__('Save Evaluation')); ?>

                                </span>
                                <span wire:loading>
                                    <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    <?php echo e(__('Saving...')); ?>

                                </span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>

            <!-- Existing Evaluation Info -->
            <!--[if BLOCK]><![endif]--><?php if($evaluation): ?>
                <div class="mt-4 pt-3 border-top">
                    <h6 class="text-muted mb-3"><?php echo e(__('Evaluation History')); ?></h6>
                    <div class="row g-2">
                        <!--[if BLOCK]><![endif]--><?php if($evaluation->assigned_at): ?>
                            <div class="col-md-6">
                                <small class="text-muted d-block"><?php echo e(__('Assigned At')); ?></small>
                                <strong><?php echo e($evaluation->assigned_at->format('M d, Y \a\t g:i A')); ?></strong>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($evaluation->started_at): ?>
                            <div class="col-md-6">
                                <small class="text-muted d-block"><?php echo e(__('Started At')); ?></small>
                                <strong><?php echo e($evaluation->started_at->format('M d, Y \a\t g:i A')); ?></strong>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($evaluation->completed_at): ?>
                            <div class="col-md-6">
                                <small class="text-muted d-block"><?php echo e(__('Completed At')); ?></small>
                                <strong><?php echo e($evaluation->completed_at->format('M d, Y \a\t g:i A')); ?></strong>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($evaluation->duration): ?>
                            <div class="col-md-6">
                                <small class="text-muted d-block"><?php echo e(__('Duration')); ?></small>
                                <strong><?php echo e($evaluation->duration); ?></strong>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($evaluation->creator): ?>
                            <div class="col-md-6">
                                <small class="text-muted d-block"><?php echo e(__('Created By')); ?></small>
                                <strong><?php echo e($evaluation->creator->name); ?></strong>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                        <!--[if BLOCK]><![endif]--><?php if($evaluation->updater): ?>
                            <div class="col-md-6">
                                <small class="text-muted d-block"><?php echo e(__('Last Updated By')); ?></small>
                                <strong><?php echo e($evaluation->updater->name); ?></strong>
                            </div>
                        <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
                    </div>
                </div>
            <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
        </div>
    </div>

    <!-- Loading Overlay -->
    <div wire:loading.flex class="position-absolute top-0 start-0 w-100 h-100 bg-white bg-opacity-75 d-flex align-items-center justify-content-center" style="z-index: 1000;">
        <div class="text-center">
            <div class="spinner-border text-primary mb-2" role="status"></div>
            <div class="text-muted"><?php echo e(__('Processing...')); ?></div>
        </div>
    </div>
</div>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/livewire/administration/hiring/stage-evaluation-form.blade.php ENDPATH**/ ?>