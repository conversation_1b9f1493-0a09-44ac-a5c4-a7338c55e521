<!-- Employee Hiring Management -->
<?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Create', 'Employee Hiring Update', 'Employee Hiring Delete'])): ?>
    <li class="menu-item <?php echo e(request()->is('hiring*') ? 'active open' : ''); ?>">
        <a href="javascript:void(0);" class="menu-link menu-toggle">
            <i class="menu-icon tf-icons ti ti-users-plus"></i>
            <div data-i18n="Employee Hiring"><?php echo e(__('Employee Hiring')); ?></div>
        </a>
        <ul class="menu-sub">
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Update', 'Employee Hiring Delete'])): ?>
                <li class="menu-item <?php echo e(request()->is('hiring/all*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.hiring.index')); ?>" class="menu-link"><?php echo e(__('All Candidates')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Create'])): ?>
                <li class="menu-item <?php echo e(request()->is('hiring/create*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.hiring.create')); ?>" class="menu-link"><?php echo e(__('Add Candidate')); ?></a>
                </li>
            <?php endif; ?>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Read'])): ?>
                <li class="menu-item <?php echo e(request()->is('hiring/my-evaluations*') ? 'active' : ''); ?>">
                    <a href="<?php echo e(route('administration.hiring.my.evaluations')); ?>" class="menu-link"><?php echo e(__('My Evaluations')); ?></a>
                </li>
            <?php endif; ?>
        </ul>
    </li>
<?php endif; ?>
<?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/layouts/administration/partials/menus/hiring.blade.php ENDPATH**/ ?>