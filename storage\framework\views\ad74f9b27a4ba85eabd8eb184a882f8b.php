<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Employee Hiring')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-bs5/datatables.bootstrap5.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/datatables-responsive-bs5/responsive.bootstrap5.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        .candidate-card {
            transition: all 0.3s ease;
        }
        .candidate-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 25px 0 rgba(0, 0, 0, 0.1);
        }
        .progress-stage {
            font-size: 0.75rem;
        }
        .stage-indicator {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin: 0 5px;
        }
        .stage-completed {
            background-color: #28a745;
            color: white;
        }
        .stage-current {
            background-color: #ffc107;
            color: #212529;
        }
        .stage-pending {
            background-color: #e9ecef;
            color: #6c757d;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Employee Hiring')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Employee Hiring')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Filters Card -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><?php echo e(__('Filter Candidates')); ?></h5>
                <button class="btn btn-sm btn-outline-secondary" type="button" data-bs-toggle="collapse" data-bs-target="#filtersCollapse">
                    <i class="ti ti-filter"></i> <?php echo e(__('Filters')); ?>

                </button>
            </div>
            <div class="collapse" id="filtersCollapse">
                <div class="card-body">
                    <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('administration.hiring.hiring-candidate-filter');

$__html = app('livewire')->mount($__name, $__params, 'lw-241198431-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Action Buttons -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h4 class="mb-0"><?php echo e(__('Hiring Candidates')); ?></h4>
                <p class="text-muted mb-0"><?php echo e(__('Manage candidate applications and hiring process')); ?></p>
            </div>
            <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Create'])): ?>
                <div>
                    <a href="<?php echo e(route('administration.hiring.create')); ?>" class="btn btn-primary">
                        <i class="ti ti-plus"></i> <?php echo e(__('Add Candidate')); ?>

                    </a>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- Candidates Grid -->
<div class="row">
    <?php $__empty_1 = true; $__currentLoopData = $candidates; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $candidate): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
        <div class="col-xl-4 col-lg-6 col-md-6 mb-4">
            <div class="card candidate-card h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <div>
                            <h5 class="card-title mb-1"><?php echo e($candidate->name); ?></h5>
                            <p class="text-muted mb-0"><?php echo e($candidate->expected_role); ?></p>
                        </div>
                        <span class="badge <?php echo e($candidate->status_badge_class); ?>">
                            <?php echo e($candidate->status_formatted); ?>

                        </span>
                    </div>

                    <div class="mb-3">
                        <small class="text-muted d-block">
                            <i class="ti ti-mail"></i> <?php echo e($candidate->email); ?>

                        </small>
                        <small class="text-muted d-block">
                            <i class="ti ti-phone"></i> <?php echo e($candidate->phone); ?>

                        </small>
                        <?php if($candidate->expected_salary): ?>
                            <small class="text-muted d-block">
                                <i class="ti ti-currency-rupee"></i> <?php echo e($candidate->expected_salary_formatted); ?>

                            </small>
                        <?php endif; ?>
                    </div>

                    <!-- Progress Stages -->
                    <div class="mb-3">
                        <div class="d-flex justify-content-center align-items-center">
                            <?php for($i = 1; $i <= 3; $i++): ?>
                                <div class="stage-indicator
                                    <?php if($i < $candidate->current_stage): ?> stage-completed
                                    <?php elseif($i == $candidate->current_stage): ?> stage-current
                                    <?php else: ?> stage-pending
                                    <?php endif; ?>">
                                    <?php echo e($i); ?>

                                </div>
                                <?php if($i < 3): ?>
                                    <div class="flex-grow-1 mx-2" style="height: 2px; background-color: #e9ecef;"></div>
                                <?php endif; ?>
                            <?php endfor; ?>
                        </div>
                        <div class="text-center mt-2">
                            <small class="text-muted progress-stage">
                                <?php echo e(__('Current Stage')); ?>: <?php echo e($candidate->current_stage_name); ?>

                            </small>
                        </div>
                    </div>

                    <div class="d-flex justify-content-between align-items-center">
                        <small class="text-muted">
                            <?php echo e(__('Added')); ?>: <?php echo e($candidate->created_at->format('M d, Y')); ?>

                        </small>
                        <div class="dropdown">
                            <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                <?php echo e(__('Actions')); ?>

                            </button>
                            <ul class="dropdown-menu">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Read'])): ?>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('administration.hiring.show', $candidate)); ?>">
                                            <i class="ti ti-eye"></i> <?php echo e(__('View Details')); ?>

                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Update'])): ?>
                                    <li>
                                        <a class="dropdown-item" href="<?php echo e(route('administration.hiring.edit', $candidate)); ?>">
                                            <i class="ti ti-edit"></i> <?php echo e(__('Edit')); ?>

                                        </a>
                                    </li>
                                <?php endif; ?>
                                <?php if($candidate->status === 'in_progress' && $candidate->current_stage >= 3): ?>
                                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything'])): ?>
                                        <li>
                                            <a class="dropdown-item" href="<?php echo e(route('administration.hiring.complete.form', $candidate)); ?>">
                                                <i class="ti ti-user-plus"></i> <?php echo e(__('Complete Hiring')); ?>

                                            </a>
                                        </li>
                                    <?php endif; ?>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Delete'])): ?>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <a class="dropdown-item text-danger" href="<?php echo e(route('administration.hiring.destroy', $candidate)); ?>"
                                           onclick="return confirm('Are you sure you want to delete this candidate?')">
                                            <i class="ti ti-trash"></i> <?php echo e(__('Delete')); ?>

                                        </a>
                                    </li>
                                <?php endif; ?>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
        <div class="col-12">
            <div class="card">
                <div class="card-body text-center py-5">
                    <i class="ti ti-users-off display-4 text-muted mb-3"></i>
                    <h5 class="text-muted"><?php echo e(__('No candidates found')); ?></h5>
                    <p class="text-muted"><?php echo e(__('Start by adding your first candidate to the hiring process.')); ?></p>
                    <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Employee Hiring Everything', 'Employee Hiring Create'])): ?>
                        <a href="<?php echo e(route('administration.hiring.create')); ?>" class="btn btn-primary">
                            <i class="ti ti-plus"></i> <?php echo e(__('Add First Candidate')); ?>

                        </a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- Pagination -->
<?php if($candidates->hasPages()): ?>
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-center">
                <?php echo e($candidates->links()); ?>

            </div>
        </div>
    </div>
<?php endif; ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2();

            // Auto-collapse filters if no filters are applied
            const hasFilters = '<?php echo e(request()->hasAny(["search", "status", "stage", "date_from", "date_to"])); ?>';
            if (hasFilters) {
                $('#filtersCollapse').addClass('show');
            }
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/hiring/index.blade.php ENDPATH**/ ?>